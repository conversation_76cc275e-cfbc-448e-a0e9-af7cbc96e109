#!/usr/bin/env python3
"""
CSS Optimization Script for GRS Application
Minifies and optimizes CSS files for production
"""

import os
import re
import gzip
from pathlib import Path

def minify_css(css_content):
    """
    Minify CSS content by removing unnecessary whitespace and comments
    """
    # Remove comments
    css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
    
    # Remove unnecessary whitespace
    css_content = re.sub(r'\s+', ' ', css_content)
    
    # Remove whitespace around specific characters
    css_content = re.sub(r'\s*([{}:;,>+~])\s*', r'\1', css_content)
    
    # Remove trailing semicolons before closing braces
    css_content = re.sub(r';\s*}', '}', css_content)
    
    # Remove leading/trailing whitespace
    css_content = css_content.strip()
    
    return css_content

def optimize_css_file(input_path, output_path):
    """
    Optimize a single CSS file
    """
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Minify the content
        minified = minify_css(content)
        
        # Write minified version
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(minified)
        
        # Create gzipped version for better compression
        gzip_path = str(output_path) + '.gz'
        with gzip.open(gzip_path, 'wt', encoding='utf-8') as f:
            f.write(minified)

        # Calculate compression stats
        original_size = len(content)
        minified_size = len(minified)
        compression_ratio = (1 - minified_size / original_size) * 100

        print(f"✅ {input_path}")
        print(f"   Original: {original_size:,} bytes")
        print(f"   Minified: {minified_size:,} bytes")
        print(f"   Saved: {compression_ratio:.1f}%")
        print(f"   Gzipped: {os.path.getsize(gzip_path):,} bytes")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error optimizing {input_path}: {e}")
        return False

def main():
    """
    Main optimization function
    """
    css_dir = Path(__file__).parent
    
    # Files to optimize
    files_to_optimize = [
        ('consolidated-critical.css', 'consolidated-critical.min.css'),
        ('consolidated-non-critical.css', 'consolidated-non-critical.min.css'),
    ]
    
    print("🚀 Starting CSS optimization...")
    print("=" * 50)
    
    success_count = 0
    total_count = len(files_to_optimize)
    
    for input_file, output_file in files_to_optimize:
        input_path = css_dir / input_file
        output_path = css_dir / output_file
        
        if input_path.exists():
            if optimize_css_file(input_path, output_path):
                success_count += 1
        else:
            print(f"⚠️  File not found: {input_path}")
    
    print("=" * 50)
    print(f"✅ Optimization complete: {success_count}/{total_count} files processed")
    
    if success_count == total_count:
        print("🎉 All CSS files optimized successfully!")
        print("\n📋 Next steps:")
        print("1. Update base.html to use minified CSS files")
        print("2. Configure web server to serve .gz files when available")
        print("3. Set appropriate cache headers for CSS files")
    else:
        print("⚠️  Some files could not be optimized. Check the errors above.")

if __name__ == "__main__":
    main()

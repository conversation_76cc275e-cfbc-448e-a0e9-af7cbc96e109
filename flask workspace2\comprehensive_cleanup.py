#!/usr/bin/env python3
"""
Comprehensive Code Cleanup Tool
Automatically cleans up Python files by removing excessive whitespace, 
unnecessary comments, and optimizing code structure.
"""

import re
from pathlib import Path

def clean_whitespace(content):
    """Remove excessive whitespace"""
    lines = content.splitlines()
    cleaned_lines = []
    
    prev_empty = False
    for line in lines:
        # Remove trailing whitespace
        line = line.rstrip()
        
        # Handle empty lines - allow max 2 consecutive empty lines
        if not line.strip():
            if not prev_empty:
                cleaned_lines.append(line)
                prev_empty = True
            # Skip additional empty lines
        else:
            cleaned_lines.append(line)
            prev_empty = False
    
    return '\n'.join(cleaned_lines)

def remove_commented_code(content):
    """Remove commented out code blocks"""
    lines = content.splitlines()
    cleaned_lines = []
    
    for line in lines:
        stripped = line.strip()
        
        # Skip lines that are clearly commented out code
        if stripped.startswith('#') and any(keyword in stripped for keyword in [
            'def ', 'class ', 'import ', 'from ', 'if ', 'for ', 'while ', 
            'try:', 'except:', 'return ', 'print(', '= ', 'cur.execute'
        ]):
            continue
        
        # Skip empty comments
        if stripped == '#':
            continue
            
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def optimize_imports(content):
    """Remove duplicate imports and organize them"""
    lines = content.splitlines()
    import_lines = []
    other_lines = []
    seen_imports = set()
    
    in_import_section = True
    
    for line in lines:
        stripped = line.strip()
        
        if stripped.startswith(('import ', 'from ')) and in_import_section:
            if stripped not in seen_imports:
                import_lines.append(line)
                seen_imports.add(stripped)
        else:
            if stripped and not stripped.startswith('#'):
                in_import_section = False
            other_lines.append(line)
    
    # Combine imports and other lines
    result_lines = import_lines
    if import_lines and other_lines:
        result_lines.append('')  # Add separator
    result_lines.extend(other_lines)
    
    return '\n'.join(result_lines)

def remove_unused_variables_simple(content):
    """Remove obviously unused variables (simple patterns)"""
    lines = content.splitlines()
    cleaned_lines = []
    
    for line in lines:
        # Skip lines with unused variables that are clearly not needed
        if re.match(r'\s*generation_date\s*=', line):
            continue
        
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def clean_function_spacing(content):
    """Ensure proper spacing around functions"""
    lines = content.splitlines()
    cleaned_lines = []
    
    for i, line in enumerate(lines):
        cleaned_lines.append(line)
        
        # Add proper spacing after function definitions
        if line.strip().startswith('def ') and i < len(lines) - 1:
            next_line = lines[i + 1] if i + 1 < len(lines) else ''
            if next_line.strip() and not next_line.startswith('    '):
                # This is the end of a function, ensure proper spacing
                if i + 1 < len(lines) and lines[i + 1].strip():
                    cleaned_lines.append('')
    
    return '\n'.join(cleaned_lines)

def comprehensive_cleanup(file_path):
    """Perform comprehensive cleanup on a Python file"""
    print(f"🧹 Cleaning up {file_path}...")
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_size = len(content)
    original_lines = len(content.splitlines())
    
    # Apply cleanup steps
    print("   • Removing excessive whitespace...")
    content = clean_whitespace(content)
    
    print("   • Removing commented out code...")
    content = remove_commented_code(content)
    
    print("   • Optimizing imports...")
    content = optimize_imports(content)
    
    print("   • Removing unused variables...")
    content = remove_unused_variables_simple(content)
    
    print("   • Cleaning function spacing...")
    content = clean_function_spacing(content)
    
    # Final whitespace cleanup
    content = clean_whitespace(content)
    
    # Calculate savings
    new_size = len(content)
    new_lines = len(content.splitlines())
    
    size_saved = original_size - new_size
    lines_saved = original_lines - new_lines
    
    print(f"   ✅ Cleanup complete!")
    print(f"      Size: {original_size} → {new_size} bytes ({size_saved} bytes saved)")
    print(f"      Lines: {original_lines} → {new_lines} lines ({lines_saved} lines saved)")
    
    # Write the cleaned content back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return {
        'original_size': original_size,
        'new_size': new_size,
        'size_saved': size_saved,
        'original_lines': original_lines,
        'new_lines': new_lines,
        'lines_saved': lines_saved
    }

def main():
    """Clean up both app.py and backend.py"""
    print("🚀 COMPREHENSIVE CODE CLEANUP")
    print("=" * 50)
    
    files_to_clean = ['backend.py']  # Now clean backend.py
    
    total_size_saved = 0
    total_lines_saved = 0
    
    for file_name in files_to_clean:
        file_path = Path(file_name)
        if file_path.exists():
            results = comprehensive_cleanup(file_path)
            total_size_saved += results['size_saved']
            total_lines_saved += results['lines_saved']
        else:
            print(f"❌ File not found: {file_path}")
    
    print("\n" + "=" * 50)
    print("🎉 CLEANUP SUMMARY")
    print(f"Total size saved: {total_size_saved} bytes")
    print(f"Total lines saved: {total_lines_saved} lines")
    print("=" * 50)

if __name__ == "__main__":
    main()

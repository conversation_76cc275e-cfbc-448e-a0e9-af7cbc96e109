{% extends "base.html" %}
{% block page_title %}Run Analysis - GRS Wall Designer{% endblock %}
{% block content %}
<div class="run-analysis-container"><div class="analysis-header"><h1>Run Analysis</h1><p class="analysis-description">
  Execute comprehensive analysis of your GRS wall design based on IS 18591:2024 standards.
  Ensure all required data has been entered and saved before running the analysis.
  </p></div><div class="analysis-workflow"><div class="workflow-step"><div class="step-icon"><i class="fas fa-check-circle"></i></div><div class="step-content"><h3>Data Validation</h3><p>Verify that all required project data has been entered and saved.</p></div></div><div class="workflow-step"><div class="step-icon"><i class="fas fa-calculator"></i></div><div class="step-content"><h3>Analysis Execution</h3><p>Perform external and internal stability calculations according to IS 18591:2024.</p></div></div><div class="workflow-step"><div class="step-icon"><i class="fas fa-chart-line"></i></div><div class="step-content"><h3>Results Generation</h3><p>Generate comprehensive analysis results and safety factor calculations.</p></div></div></div><div class="analysis-section"><div class="analysis-card"><h2>Ready to Run Analysis?</h2><p>
  After saving all inputs from the previous sections, click below to run
  the comprehensive analysis of your GRS wall design.
  </p><button id="run-analysis-btn" class="cta-button">Run Analysis</button></div><div id="analysis-results" class="analysis-results-section" style="display: none"><h3>Analysis Complete</h3><div id="result-buttons" class="result-buttons-grid"><a
          href="{{ url_for('external_stability_results') }}"
    class="btn btn-primary result-btn"
    >External Stability Results</a
  ><a
          href="{{ url_for('internal_stability_results') }}"
    class="btn btn-primary result-btn"
    >Internal Stability Results</a
  ></div><div class="satisfaction-section"><label for="satisfaction-checkbox" class="satisfaction-label"><input
    type="checkbox"
    id="satisfaction-checkbox"
    class="form-check-input"
    />
    Are you satisfied with the design configuration?
  </label></div><div id="generate-report-container" class="generate-report-section" style="display: none"><div class="alert alert-info mb-3"><strong>Note:</strong> For the best report quality, please take a screenshot from the
    <a href="/reinforcementlayout" target="_blank">Reinforcement Layout</a> section before generating the report.
    The screenshot will be automatically included in your report.
  </div><button id="generate-report-btn" class="btn btn-success report-btn">
    Generate Report
  </button></div></div><div
  id="error-message"
  class="alert alert-danger error-message"
  style="display: none"
  ></div></div></div><script>
  document.addEventListener("DOMContentLoaded", function () {
  const runAnalysisBtn = document.getElementById("run-analysis-btn");
  const analysisResults = document.getElementById("analysis-results");
  const satisfactionCheckbox = document.getElementById(
  "satisfaction-checkbox"
  );
  const generateReportContainer = document.getElementById(
  "generate-report-container"
  );
  const generateReportBtn = document.getElementById("generate-report-btn");
  const errorMessage = document.getElementById("error-message");

  if (localStorage.getItem("analysisRun") === "true") {
  analysisResults.style.display = "block";
  }

  runAnalysisBtn.addEventListener("click", function () {
  fetch("/run_analysis", { method: "POST" })
  .then((response) => response.json())
  .then((data) => {
    if (data.has_results) {
    analysisResults.style.display = "block";
    localStorage.setItem("analysisRun", "true");
    errorMessage.style.display = "none";
    } else {
    errorMessage.textContent = data.error;
    errorMessage.style.display = "block";
    analysisResults.style.display = "none";
    }
  })
  .catch((error) => {
    console.error("Error:", error);
    errorMessage.textContent =
    "An error occurred while running the analysis.";
    errorMessage.style.display = "block";
  });
  });

  satisfactionCheckbox.addEventListener("change", function () {
  generateReportContainer.style.display = this.checked ? "block" : "none";
  });

  generateReportBtn.addEventListener("click", function () {
  window.location.href = "/generate_report";
  });
  });
</script>

{% endblock %}
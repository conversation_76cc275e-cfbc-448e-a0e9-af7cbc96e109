#!/usr/bin/env python3
"""
Code Cleanup Analyzer
Analyzes Python files for cleanup opportunities including unused imports, variables, 
excessive whitespace, unnecessary comments, and code duplication.
"""

import ast
import re
from pathlib import Path
from collections import defaultdict

class CodeCleanupAnalyzer:
    def __init__(self, file_path):
        self.file_path = Path(file_path)
        self.content = self.file_path.read_text(encoding='utf-8')
        self.lines = self.content.splitlines()
        self.issues = defaultdict(list)
        
    def analyze_imports(self):
        """Find unused imports"""
        print(f"🔍 Analyzing imports in {self.file_path.name}...")
        
        # Parse AST to find imports
        try:
            tree = ast.parse(self.content)
        except SyntaxError as e:
            print(f"❌ Syntax error in {self.file_path.name}: {e}")
            return
            
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for alias in node.names:
                        imports.append(f"{node.module}.{alias.name}" if alias.name != '*' else node.module)
        
        # Check usage of each import
        unused_imports = []
        for imp in imports:
            base_name = imp.split('.')[-1]
            # Simple regex check for usage (not perfect but good enough)
            if not re.search(rf'\b{re.escape(base_name)}\b', self.content.replace(f'import {imp}', '').replace(f'from {imp}', '')):
                unused_imports.append(imp)
        
        self.issues['unused_imports'] = unused_imports
        print(f"   Found {len(unused_imports)} potentially unused imports")
        
    def analyze_whitespace(self):
        """Find excessive whitespace issues"""
        print(f"🔍 Analyzing whitespace in {self.file_path.name}...")
        
        whitespace_issues = []
        
        # Find lines with excessive spaces
        for i, line in enumerate(self.lines, 1):
            # Multiple consecutive empty lines (more than 2)
            if i > 2 and all(not self.lines[j-1].strip() for j in range(i-2, i+1)):
                whitespace_issues.append(f"Line {i}: Excessive empty lines")
            
            # Lines with trailing whitespace
            if line.rstrip() != line:
                whitespace_issues.append(f"Line {i}: Trailing whitespace")
            
            # Lines with excessive indentation (more than 8 levels)
            if len(line) - len(line.lstrip()) > 32:  # 4 spaces * 8 levels
                whitespace_issues.append(f"Line {i}: Excessive indentation")
        
        self.issues['whitespace'] = whitespace_issues
        print(f"   Found {len(whitespace_issues)} whitespace issues")
        
    def analyze_comments(self):
        """Find unnecessary comments and documentation"""
        print(f"🔍 Analyzing comments in {self.file_path.name}...")
        
        comment_issues = []
        
        for i, line in enumerate(self.lines, 1):
            stripped = line.strip()
            
            # Empty comment lines
            if stripped == '#':
                comment_issues.append(f"Line {i}: Empty comment")
            
            # Very short comments that don't add value
            if stripped.startswith('#') and len(stripped) < 10 and not any(word in stripped.lower() for word in ['todo', 'fixme', 'hack', 'note']):
                comment_issues.append(f"Line {i}: Very short comment: {stripped}")
            
            # Commented out code (lines starting with # followed by code-like content)
            if stripped.startswith('#') and any(keyword in stripped for keyword in ['def ', 'class ', 'import ', 'from ', 'if ', 'for ', 'while ']):
                comment_issues.append(f"Line {i}: Commented out code: {stripped[:50]}...")
        
        self.issues['comments'] = comment_issues
        print(f"   Found {len(comment_issues)} comment issues")
        
    def analyze_duplicates(self):
        """Find duplicate code blocks"""
        print(f"🔍 Analyzing duplicates in {self.file_path.name}...")
        
        duplicates = []
        
        # Look for duplicate function definitions
        function_bodies = {}
        for i, line in enumerate(self.lines):
            if line.strip().startswith('def '):
                func_name = line.strip().split('(')[0].replace('def ', '')
                # Get function body (simplified)
                body_lines = []
                j = i + 1
                while j < len(self.lines) and (self.lines[j].startswith('    ') or not self.lines[j].strip()):
                    if self.lines[j].strip():
                        body_lines.append(self.lines[j].strip())
                    j += 1
                
                body_hash = hash('\n'.join(body_lines))
                if body_hash in function_bodies:
                    duplicates.append(f"Function '{func_name}' similar to '{function_bodies[body_hash]}' at line {i+1}")
                else:
                    function_bodies[body_hash] = func_name
        
        # Look for duplicate import blocks
        import_lines = [line for line in self.lines if line.strip().startswith(('import ', 'from '))]
        seen_imports = set()
        for line in import_lines:
            if line in seen_imports:
                duplicates.append(f"Duplicate import: {line.strip()}")
            seen_imports.add(line)
        
        self.issues['duplicates'] = duplicates
        print(f"   Found {len(duplicates)} potential duplicates")
        
    def analyze_variables(self):
        """Find unused variables (basic analysis)"""
        print(f"🔍 Analyzing variables in {self.file_path.name}...")
        
        # This is a simplified analysis - would need more sophisticated AST parsing for accuracy
        unused_vars = []
        
        # Find variable assignments
        var_assignments = []
        for i, line in enumerate(self.lines, 1):
            # Simple regex for variable assignment
            match = re.match(r'\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*=', line)
            if match and not line.strip().startswith('#'):
                var_name = match.group(1)
                # Skip common patterns
                if var_name not in ['self', 'app', 'result', 'response', 'data', 'session', 'request']:
                    var_assignments.append((var_name, i))
        
        # Check if variables are used elsewhere
        for var_name, line_num in var_assignments:
            # Count occurrences (excluding the assignment line)
            content_without_assignment = '\n'.join(self.lines[:line_num-1] + self.lines[line_num:])
            if content_without_assignment.count(var_name) == 0:
                unused_vars.append(f"Line {line_num}: Unused variable '{var_name}'")
        
        self.issues['unused_variables'] = unused_vars
        print(f"   Found {len(unused_vars)} potentially unused variables")
        
    def analyze_large_functions(self):
        """Find overly large functions"""
        print(f"🔍 Analyzing function sizes in {self.file_path.name}...")
        
        large_functions = []
        current_function = None
        function_start = 0
        
        for i, line in enumerate(self.lines):
            if line.strip().startswith('def '):
                if current_function:
                    func_length = i - function_start
                    if func_length > 50:  # Functions longer than 50 lines
                        large_functions.append(f"Function '{current_function}' at line {function_start+1}: {func_length} lines")
                
                current_function = line.strip().split('(')[0].replace('def ', '')
                function_start = i
        
        # Check the last function
        if current_function:
            func_length = len(self.lines) - function_start
            if func_length > 50:
                large_functions.append(f"Function '{current_function}' at line {function_start+1}: {func_length} lines")
        
        self.issues['large_functions'] = large_functions
        print(f"   Found {len(large_functions)} large functions")
        
    def run_full_analysis(self):
        """Run all analyses"""
        print(f"\n🧹 ANALYZING {self.file_path.name.upper()}")
        print("=" * 50)
        
        self.analyze_imports()
        self.analyze_whitespace()
        self.analyze_comments()
        self.analyze_duplicates()
        self.analyze_variables()
        self.analyze_large_functions()
        
        return self.issues
        
    def generate_cleanup_report(self):
        """Generate a detailed cleanup report"""
        total_issues = sum(len(issues) for issues in self.issues.values())
        
        print(f"\n📊 CLEANUP REPORT FOR {self.file_path.name.upper()}")
        print("=" * 50)
        print(f"Total issues found: {total_issues}")
        print()
        
        for category, issues in self.issues.items():
            if issues:
                print(f"🔸 {category.replace('_', ' ').title()}: {len(issues)} issues")
                for issue in issues[:5]:  # Show first 5 issues
                    print(f"   • {issue}")
                if len(issues) > 5:
                    print(f"   ... and {len(issues) - 5} more")
                print()
        
        return self.issues

def analyze_files():
    """Analyze both app.py and backend.py"""
    files_to_analyze = [
        Path("app.py"),
        Path("backend.py")
    ]
    
    all_results = {}
    
    for file_path in files_to_analyze:
        if file_path.exists():
            analyzer = CodeCleanupAnalyzer(file_path)
            results = analyzer.run_full_analysis()
            analyzer.generate_cleanup_report()
            all_results[str(file_path)] = results
        else:
            print(f"❌ File not found: {file_path}")
    
    # Summary
    print("\n🎯 OVERALL CLEANUP SUMMARY")
    print("=" * 50)
    
    total_issues = 0
    for file_path, results in all_results.items():
        file_issues = sum(len(issues) for issues in results.values())
        total_issues += file_issues
        print(f"{Path(file_path).name}: {file_issues} issues")
    
    print(f"\nTotal issues across all files: {total_issues}")
    print("\n✅ Analysis complete! Ready for cleanup.")
    
    return all_results

if __name__ == "__main__":
    analyze_files()

#!/usr/bin/env python3
"""
Flask Backend Optimizer
Analyzes and optimizes Flask routes, session management, database queries, and server-side performance
"""

import os
import re
import ast
from pathlib import Path
import time

class FlaskBackendOptimizer:
    def __init__(self, app_file, backend_file):
        self.app_file = Path(app_file)
        self.backend_file = Path(backend_file)
        self.optimization_results = {}
        
    def analyze_performance_bottlenecks(self):
        """Analyze Flask app for performance bottlenecks"""
        print("🔍 Analyzing Flask Backend Performance Bottlenecks...")
        
        bottlenecks = {
            'database_queries': [],
            'session_management': [],
            'route_inefficiencies': [],
            'memory_issues': [],
            'calculation_performance': []
        }
        
        # Analyze app.py
        with open(self.app_file, 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # Database query analysis
        db_patterns = [
            (r'cur\.execute\([^)]+\)', 'Database query without connection pooling'),
            (r'mysql\.connection\.cursor\(\)', 'New cursor creation - potential connection leak'),
            (r'SELECT \* FROM', 'SELECT * query - inefficient column selection'),
            (r'cur\.fetchone\(\)[0]', 'Direct index access on query result'),
        ]
        
        for pattern, issue in db_patterns:
            matches = re.findall(pattern, app_content)
            if matches:
                bottlenecks['database_queries'].append({
                    'issue': issue,
                    'count': len(matches),
                    'examples': matches[:3]
                })
        
        # Session management analysis
        session_patterns = [
            (r'session\.get\([^)]+\)', 'Session access'),
            (r'session\[[^]]+\]\s*=', 'Session write operation'),
            (r'clear_all_session_data\(\)', 'Full session clear - expensive operation'),
        ]
        
        for pattern, issue in session_patterns:
            matches = re.findall(pattern, app_content)
            if matches:
                bottlenecks['session_management'].append({
                    'issue': issue,
                    'count': len(matches),
                    'severity': 'high' if len(matches) > 50 else 'medium'
                })
        
        # Route inefficiency analysis
        route_issues = []
        if 'validate_user_session()' in app_content:
            route_issues.append('Session validation on every request - expensive')
        if app_content.count('@app.before_request') > 1:
            route_issues.append('Multiple before_request handlers - performance impact')
        if 'mysql.connection.cursor()' in app_content:
            route_issues.append('Database connections in routes without pooling')
        
        bottlenecks['route_inefficiencies'] = route_issues
        
        # Analyze backend.py for calculation performance
        with open(self.backend_file, 'r', encoding='utf-8') as f:
            backend_content = f.read()
        
        calc_issues = []
        if backend_content.count('float(') > 100:
            calc_issues.append(f'Excessive float conversions: {backend_content.count("float(")} occurrences')
        if backend_content.count('math.') > 200:
            calc_issues.append(f'Heavy math operations: {backend_content.count("math.")} math calls')
        if 'try:' in backend_content and backend_content.count('except') < backend_content.count('try:'):
            calc_issues.append('Incomplete error handling in calculations')
        
        bottlenecks['calculation_performance'] = calc_issues
        
        return bottlenecks
    
    def optimize_database_operations(self):
        """Optimize database operations and connection management"""
        print("🗄️  Optimizing Database Operations...")
        
        optimizations = []
        
        # Read current app.py
        with open(self.app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add connection pooling configuration
        if 'MYSQL_POOL_SIZE' not in content:
            pool_config = '''
# Database Connection Pool Configuration
app.config['MYSQL_POOL_SIZE'] = 10
app.config['MYSQL_POOL_TIMEOUT'] = 20
app.config['MYSQL_POOL_RECYCLE'] = 3600
app.config['MYSQL_AUTOCOMMIT'] = True
'''
            content = content.replace('mysql = MySQL(app)', pool_config + '\nmysql = MySQL(app)')
            optimizations.append('Added database connection pooling configuration')
        
        # Add database context manager
        db_context_manager = '''
from contextlib import contextmanager

@contextmanager
def get_db_cursor():
    """Database cursor context manager for automatic cleanup"""
    cur = None
    try:
        cur = mysql.connection.cursor()
        yield cur
        mysql.connection.commit()
    except Exception as e:
        if mysql.connection:
            mysql.connection.rollback()
        raise e
    finally:
        if cur:
            cur.close()
'''
        
        if 'get_db_cursor' not in content:
            # Insert after imports
            import_end = content.find('mysql = MySQL(app)')
            if import_end != -1:
                insert_pos = content.find('\n', import_end) + 1
                content = content[:insert_pos] + db_context_manager + content[insert_pos:]
                optimizations.append('Added database context manager for automatic cleanup')
        
        # Write optimized content
        with open(self.app_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return optimizations
    
    def optimize_session_management(self):
        """Optimize session management and caching"""
        print("🔐 Optimizing Session Management...")
        
        optimizations = []
        
        with open(self.app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add session caching mechanism
        session_cache = '''
from functools import lru_cache
import time

# Session validation cache to reduce database hits
session_validation_cache = {}
CACHE_TIMEOUT = 300  # 5 minutes

def get_cached_session_validation(session_id, user_id):
    """Get cached session validation result"""
    cache_key = f"{session_id}:{user_id}"
    if cache_key in session_validation_cache:
        cached_time, result = session_validation_cache[cache_key]
        if time.time() - cached_time < CACHE_TIMEOUT:
            return result
    return None

def cache_session_validation(session_id, user_id, result):
    """Cache session validation result"""
    cache_key = f"{session_id}:{user_id}"
    session_validation_cache[cache_key] = (time.time(), result)
    
    # Clean old cache entries
    current_time = time.time()
    expired_keys = [k for k, (t, _) in session_validation_cache.items() 
                   if current_time - t > CACHE_TIMEOUT]
    for key in expired_keys:
        del session_validation_cache[key]
'''
        
        if 'session_validation_cache' not in content:
            # Insert after imports
            mysql_pos = content.find('mysql = MySQL(app)')
            if mysql_pos != -1:
                insert_pos = content.find('\n', mysql_pos) + 1
                content = content[:insert_pos] + session_cache + content[insert_pos:]
                optimizations.append('Added session validation caching mechanism')
        
        # Optimize session data access patterns
        session_optimizations = '''
def get_session_data_batch(keys):
    """Get multiple session values efficiently"""
    return {key: session.get(key) for key in keys}

def update_session_data_batch(data_dict):
    """Update multiple session values efficiently"""
    session.update(data_dict)
'''
        
        if 'get_session_data_batch' not in content:
            content += session_optimizations
            optimizations.append('Added batch session data operations')
        
        with open(self.app_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return optimizations
    
    def optimize_route_performance(self):
        """Optimize route performance and request handling"""
        print("🛣️  Optimizing Route Performance...")
        
        optimizations = []
        
        with open(self.app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add response caching for static data
        caching_imports = '''
from flask_caching import Cache
from functools import wraps
import hashlib

# Configure caching
cache = Cache(app, config={'CACHE_TYPE': 'simple'})

def cache_response(timeout=300):
    """Decorator for caching route responses"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Create cache key from route and user
            cache_key = f"{request.endpoint}:{session.get('user_id', 'anonymous')}"
            cached_response = cache.get(cache_key)
            if cached_response:
                return cached_response
            
            response = f(*args, **kwargs)
            cache.set(cache_key, response, timeout=timeout)
            return response
        return decorated_function
    return decorator
'''
        
        if 'flask_caching' not in content:
            # Add after Flask imports
            flask_import_pos = content.find('from flask import')
            if flask_import_pos != -1:
                line_end = content.find('\n', flask_import_pos)
                content = content[:line_end] + '\n' + caching_imports + content[line_end:]
                optimizations.append('Added response caching mechanism')
        
        # Add request optimization middleware
        request_optimization = '''
@app.before_request
def optimize_request():
    """Optimize request processing"""
    # Skip optimization for static files
    if request.endpoint == 'static':
        return
    
    # Add request timing
    request.start_time = time.time()

@app.after_request
def log_request_performance(response):
    """Log request performance metrics"""
    if hasattr(request, 'start_time'):
        duration = time.time() - request.start_time
        if duration > 1.0:  # Log slow requests
            print(f"Slow request: {request.endpoint} took {duration:.2f}s")
    return response
'''
        
        if 'optimize_request' not in content:
            content += request_optimization
            optimizations.append('Added request performance monitoring')
        
        with open(self.app_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return optimizations
    
    def optimize_calculation_performance(self):
        """Optimize backend calculation performance"""
        print("🧮 Optimizing Calculation Performance...")
        
        optimizations = []
        
        with open(self.backend_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add calculation caching
        calc_optimization = '''
from functools import lru_cache
import json
import hashlib

def create_calculation_hash(session_data):
    """Create hash of calculation inputs for caching"""
    relevant_keys = [
        'geometryData', 'soil_density', 'friction_angle', 'cohesion',
        'retainedsoil_density', 'retainedfriction_angle', 'retainedcohesion',
        'foundationsoildensity', 'foundationsoilfriction_angle', 'foundationsoilcohesion',
        'eccentricity', 'eccentricity_seismic', 'watertable', 'externalloads_data'
    ]
    
    input_data = {key: session_data.get(key) for key in relevant_keys}
    input_str = json.dumps(input_data, sort_keys=True)
    return hashlib.md5(input_str.encode()).hexdigest()

# Calculation result cache
calculation_cache = {}
CALC_CACHE_SIZE = 100

'''
        
        if 'create_calculation_hash' not in content:
            content = calc_optimization + content
            optimizations.append('Added calculation result caching')
        
        # Optimize float conversions
        original_float_count = content.count('float(')
        
        # Replace repetitive float conversions with batch processing
        float_optimization = '''
def safe_float_conversion(value, default=0):
    """Safely convert value to float with default"""
    try:
        return float(value) if value else default
    except (ValueError, TypeError):
        return default

def batch_float_conversion(data_dict, keys, default=0):
    """Convert multiple values to float efficiently"""
    return {key: safe_float_conversion(data_dict.get(key), default) for key in keys}
'''
        
        if 'safe_float_conversion' not in content:
            content = float_optimization + content
            optimizations.append('Added efficient float conversion utilities')
        
        with open(self.backend_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return optimizations

def main():
    """Main optimization process"""
    print("🚀 Flask Backend Optimization Starting...")
    print("=" * 60)
    
    # Initialize optimizer
    app_file = Path(__file__).parent / 'app.py'
    backend_file = Path(__file__).parent / 'backend.py'
    
    optimizer = FlaskBackendOptimizer(app_file, backend_file)
    
    # Analyze bottlenecks
    bottlenecks = optimizer.analyze_performance_bottlenecks()
    
    # Print analysis results
    print("📊 PERFORMANCE BOTTLENECK ANALYSIS")
    print("-" * 40)
    
    for category, issues in bottlenecks.items():
        if issues:
            print(f"\n🔴 {category.replace('_', ' ').title()}:")
            if isinstance(issues, list) and issues and isinstance(issues[0], dict):
                for issue in issues:
                    print(f"   • {issue['issue']}: {issue.get('count', 'N/A')} occurrences")
            else:
                for issue in issues:
                    print(f"   • {issue}")
    
    # Apply optimizations
    print("\n🔧 APPLYING OPTIMIZATIONS...")
    print("-" * 40)
    
    db_opts = optimizer.optimize_database_operations()
    session_opts = optimizer.optimize_session_management()
    route_opts = optimizer.optimize_route_performance()
    calc_opts = optimizer.optimize_calculation_performance()
    
    all_optimizations = db_opts + session_opts + route_opts + calc_opts
    
    print(f"\n✅ Applied {len(all_optimizations)} optimizations:")
    for i, opt in enumerate(all_optimizations, 1):
        print(f"{i}. {opt}")
    
    print("\n" + "=" * 60)
    print("🎉 FLASK BACKEND OPTIMIZATION COMPLETED!")
    print("=" * 60)

if __name__ == "__main__":
    main()

{% extends "base.html" %} {% block content %}
<h1>Geometry Inputs</h1><form
  id="geometry-form"
  method="POST"
  action="javascript:void(0);"
  onsubmit="return false;"
><div class="form-group"><label for="wall-height">Wall Height (H: m):</label><input
  type="number"
  id="wall-height"
  name="wall_height"
  step="0.1"
  required
  placeholder="5.0"
  min="0"
      value="{{ wallHeight or '' }}"
  /><span class="error-message" id="wall-height-error"></span></div><div class="form-group"><label for="embedment-depth">Embedment Depth (m):</label><input
  type="number"
  id="embedment-depth"
  name="embedment_depth"
  step="0.1"
  required
  placeholder="1.0"
  min="0"
      value="{{ embedmentDepth or '' }}"
  /><span class="error-message" id="embedment-depth-error"></span></div><div class="form-group"><label for="wall-length">Overall Length of Reinforcement (m):</label><input
  type="number"
  id="wall-length"
  name="wall_length"
  step="0.1"
  required
  placeholder="6.0"
  min="0"
      value="{{ wallLength or '' }}"
  /><span class="error-message" id="wall-length-error"></span></div><div class="form-group"><label for="wall-batter">Wall Batter (degrees):</label><input
  type="number"
  id="wall-batter"
  name="wall_batter"
  step="0.1"
  required
  placeholder="0.0"
  min="0"
  max="90"
      value="{{ wallBatter or '' }}"
  /><span class="error-message" id="wall-batter-error"></span></div><div class="form-group"><label for="backslope-angle">Backslope Angle (degrees):</label><input
  type="number"
  id="backslope-angle"
  name="backslope_angle"
  step="0.1"
  required
  placeholder="0.0"
  min="0"
  max="90"
      value="{{ backslopeAngle or '' }}"
  /><span class="error-message" id="backslope-angle-error"></span></div><div class="form-group"><label for="backslope-rise">Backslope Rise (m):</label><input
  type="number"
  id="backslope-rise"
  name="backslope_rise"
  step="0.1"
  required
  placeholder="2.0"
  min="0"
      value="{{ backslopeRise or '' }}"
  /><span class="error-message" id="backslope-rise-error"></span></div><button type="submit" id="save-button">Save Geometry</button></form><div class="note"><p><strong>Note:</strong> The default values are: Wall Height = 5m, Embedment
  Depth = 1m, Reinforcement Length = 6m, Wall Batter = 0°, Backslope Angle =
  0°, Backslope Rise = 2m.
  </p></div><div class="note"><p><strong>Tip:</strong> The visualization updates automatically as you change
  input values. You only need to click "Save Geometry" to store your settings
  for later.
  </p></div><section class="visualization-section"><h2>Geometry Visualization</h2><div class="visualization-container"><div class="canvas-wrapper"><canvas id="geometry-canvas" width="800" height="500"></canvas></div><div class="button-group"><button id="zoom-in-button" class="btn btn-primary">Zoom In</button><button id="zoom-out-button" class="btn btn-primary">Zoom Out</button><button id="fit-button" class="btn btn-primary">Fit to Window</button><button id="screenshot-button" class="btn btn-primary">
  Take Screenshot
  </button></div></div></section><script>
  const form = document.getElementById("geometry-form");

  // Validation rules
  const validations = {
  "wall-height": {
  test: (value) => value > 0,
  message: "Wall height must be greater than 0",
  },
  "embedment-depth": {
  test: (value) => value >= 0,
  message: "Embedment depth cannot be negative",
  },
  "wall-length": {
  test: (value) => value > 0,
  message: "Reinforcement length must be greater than 0",
  },
  "wall-batter": {
  test: (value) => value >= 0 && value <= 90,
  message: "Wall batter must be between 0 and 90 degrees",
  },
  "backslope-angle": {
  test: (value) => value >= 0 && value <= 90,
  message: "Backslope angle must be between 0 and 90 degrees",
  },
  "backslope-rise": {
  test: (value) => value >= 0,
  message: "Backslope rise cannot be negative",
  },
  };

  // Validate individual field
  function validateField(input) {
  const fieldId = input.id;
  const errorElement = document.getElementById(`${fieldId}-error`);
  const validation = validations[fieldId];
  const value = parseFloat(input.value);

  if (isNaN(value) || input.value.trim() === "") {
  errorElement.textContent = "This field is required";
  input.classList.add("invalid");
  return false;
  } else if (validation && !validation.test(value)) {
  errorElement.textContent = validation.message;
  input.classList.add("invalid");
  return false;
  } else {
  errorElement.textContent = "";
  input.classList.remove("invalid");
  return true;
  }
  }

  // Add input event listeners for real-time validation (but not for dynamic updates - handled by geometryVisualization.js)
  form.querySelectorAll("input").forEach((input) => {
  input.addEventListener("blur", () => validateField(input));
  });

  // Ensure form submission is handled by AJAX (backup handler)
  form.addEventListener("submit", function (event) {
  console.log("🔥 Geometry form submit event triggered");

  // ALWAYS prevent default form submission to avoid JSON page redirect
  event.preventDefault();
  event.stopPropagation();

  // Check if formHandler.js is attached and let it handle the submission
  if (form.hasAttribute("data-form-handler-attached")) {
  console.log("✅ FormHandler.js is attached - triggering its handler");
  // Manually trigger the formHandler.js submission
  if (typeof handleFormSubmission === "function") {
  const config = window.FORM_CONFIGS
    ? window.FORM_CONFIGS["geometry-form"]
    : null;
  if (config) {
    handleFormSubmission(form, config);
    return;
  }
  }
  }

  console.log("⚠️ Using fallback AJAX handler");

  // Validate all fields
  let isValid = true;
  form.querySelectorAll("input").forEach((input) => {
  if (!validateField(input)) {
  isValid = false;
  }
  });

  if (!isValid) {
  if (typeof showErrorPopup === "function") {
  showErrorPopup("Please fix the errors in the form before submitting.");
  } else {
  alert("Please fix the errors in the form before submitting.");
  }
  return;
  }

  const formData = new FormData(form);
  fetch("/geometry", {
  method: "POST",
  body: formData,
  })
  .then((response) => response.json())
  .then((data) => {
  // Save geometry data to localStorage
  const geometryData = {
    wallHeight: parseFloat(document.getElementById("wall-height").value),
    embedmentDepth: parseFloat(
    document.getElementById("embedment-depth").value
    ),
    wallLength: parseFloat(document.getElementById("wall-length").value),
    wallBatter: parseFloat(document.getElementById("wall-batter").value),
    backslopeAngle: parseFloat(
    document.getElementById("backslope-angle").value
    ),
    backslopeRise: parseFloat(
    document.getElementById("backslope-rise").value
    ),
  };
  localStorage.setItem("geometryData", JSON.stringify(geometryData));
  console.log("✅ Fallback handler: Showing success popup");
  if (typeof showSuccessPopup === "function") {
    showSuccessPopup(data.message);
  } else {
    alert(data.message);
  }

  // Update sidebar status indicator
  if (typeof updateSidebarStatus === "function") {
    updateSidebarStatus("geometry-form");
  }

  // Redraw canvas after successful save
  if (typeof redrawCanvas === "function") {
    setTimeout(() => redrawCanvas(), 100);
  }
  })
  .catch((error) => {
  console.error("Error:", error);
  if (typeof showErrorPopup === "function") {
    showErrorPopup("Error saving geometry data.");
  } else {
    alert("Error saving geometry data.");
  }
  });
  });

  // Load from localStorage if available
  document.addEventListener("DOMContentLoaded", function () {
  // Check if geometry form elements exist
  const wallHeightEl = document.getElementById("wall-height");
  if (!wallHeightEl) {
  console.warn("Geometry form elements not found, skipping localStorage load");
  return;
  }

  const savedData = JSON.parse(localStorage.getItem("geometryData"));
  if (savedData) {
  const elements = {
  "wall-height": savedData.wallHeight || "",
  "embedment-depth": savedData.embedmentDepth || "",
  "wall-length": savedData.wallLength || "",
  "wall-batter": savedData.wallBatter || "",
  "backslope-angle": savedData.backslopeAngle || "",
  "backslope-rise": savedData.backslopeRise || ""
  };

  Object.entries(elements).forEach(([id, value]) => {
  const element = document.getElementById(id);
  if (element) {
    element.value = value;
  }
  });
  }
  });
</script><style>
  body {
  background-color: #f9f6f2;
  margin: 0;
  padding: 20px;
  color: #4d3d36;
  }

  h2 {
  color: #8c5a3c;
  text-align: center;
  margin-bottom: 20px;
  }

  .visualization-section {
  background: linear-gradient(145deg, #fdfaf6, #efe7dc);
  border-radius: 16px;
  box-shadow: 8px 8px 20px rgba(0, 0, 0, 0.05),
  -8px -8px 20px rgba(255, 255, 255, 0.8);
  padding: 30px;
  margin: 30px auto;
  width: 90%;
  max-width: 900px;
  border: 1px solid #d6c4b1;
  }

  .visualization-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  }

  .canvas-wrapper {
  background: linear-gradient(145deg, #f5e6d7, #e6d3b8);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid #c4a785;
  width: 100%;
  max-width: 800px;
  overflow: hidden;
  box-sizing: border-box;
  box-shadow: inset 6px 6px 12px rgba(0, 0, 0, 0.05),
  inset -6px -6px 12px rgba(255, 255, 255, 0.8);
  }

  canvas {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  background-color: #fff;
  border: 1px solid #c4a785;
  }

  .button-group {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
  }

  /* Button group styling removed to match external loads section */
  /* Buttons now inherit from global styles for consistency */

  .form-group {
  margin-bottom: 15px;
  }

  .form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  }

  .form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  }

  .error-message {
  color: #d9534f;
  font-size: 0.85em;
  margin-top: 4px;
  display: block;
  font-weight: 500;
  }

  input.invalid {
  border: 1px solid #d9534f;
  background-color: #fff8f8;
  }

  #save-button {
  margin-top: 20px;
  padding: 10px 15px;
  background-color: #a47551;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  }

  #save-button:hover {
  background-color: #8c5a3c;
  }

  .note {
  margin-top: 20px;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-left: 4px solid #a47551;
  border-radius: 4px;
  }
</style><script src="{{ url_for('static', filename='js/consolidated-visualization-optimized.js') }}"></script>
{% endblock %}
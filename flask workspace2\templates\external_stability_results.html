
{% extends "base.html" %}
{% block content %}
<!-- Note: Now using consolidated results.css from base.html -->
<h2>External Stability Analysis Results</h2>


{% if not results %}
<div class="alert alert-danger">
  <strong>Error:</strong> The results dictionary is empty or not passed correctly.
</div>
{% endif %}


<div class="card mb-4">
  <div class="card-header d-flex justify-content-between align-items-center">
  <h3>Sliding Check</h3>
  <button class="btn btn-sm btn-outline-secondary" onclick="copyTable('slidingTable')">Copy</button>
  </div>
  <div class="card-body">
  <table class="table table-striped" id="slidingTable">
    <thead>
    <tr>
    <th>Load Case</th>
    <th>Horizontal Load (kN/m)</th>
    <th>Vertical Load (kN/m)</th>
    <th>Sliding Resistance (kN/m)</th>
    <th>CDR Sliding</th>
    </tr>
    </thead>
    <tbody>
                {% for case in ['Maximum', 'Minimum', 'Critical'] %}
    <tr>
                    <td>{{ case }}</td>
                    <td>{{ results.get('Horizontal Load for sliding check', {}).get(case, 0) | round(2) }}</td>
                    <td>{{ results.get('Vertical Load for sliding check', {}).get(case, 0) | round(2) }}</td>
                    <td>{{ results.get('Sliding Resistance for sliding check', {}).get(case, 0) | round(2) }}</td>
                    <td {% if results.get('CDR Sliding for sliding check', {}).get(case, 0) < 1 %}class="text-danger"{% else %}class="text-success"{% endif %}>
                        {{ results.get('CDR Sliding for sliding check', {}).get(case, 0) | round(2) }}
    </td>
    </tr>
                {% endfor %}
    <tr>
    <td>Earthquake</td>
                    <td>{{ results.get('Earthqauke case Values for sliding check', {}).get('EQRh', 0) | round(2) }}</td>
                    <td>{{ results.get('Earthqauke case Values for sliding check', {}).get('EQRv', 0) | round(2) }}</td>
                    <td>{{ results.get('Earthqauke case Values for sliding check', {}).get('EQ Sliding Resistance', 0) | round(2) }}</td>
                    <td {% if results.get('Earthqauke case Values for sliding check', {}).get('EQ CDR Sliding', 0) < 1 %}class="text-danger"{% else %}class="text-success"{% endif %}>
                        {{ results.get('Earthqauke case Values for sliding check', {}).get('EQ CDR Sliding', 0) | round(2) }}
    </td>
    </tr>
    </tbody>
  </table>
  </div>
</div>


<div class="card mb-4">
  <div class="card-header d-flex justify-content-between align-items-center">
  <h3>Bearing Capacity Check</h3>
  <button class="btn btn-sm btn-outline-secondary" onclick="copyTable('bearingTable')">Copy</button>
  </div>
  <div class="card-body">
  <table class="table table-striped" id="bearingTable">
    <thead>
    <tr>
    <th>Load Case</th>
    <th>Vertical Load (kN/m)</th>
    <th>Overturning Moment (kN.m/m)</th>
    <th>Resistance Moment (kN.m/m)</th>
    <th>Bearing Stress (kPa)</th>
    <th>Net Bearing Capacity (kPa)</th>
    <th>CDR Bearing</th>
    </tr>
    </thead>
    <tbody>
                {% for case in ['Maximum', 'Minimum', 'Critical', 'Service'] %}
    <tr>
                    <td>{{ case }}</td>
                    <td>{{ results.get('Rv for bearing check', {}).get('Rv ' + case, 0) | round(2) }}</td>
                    <td>{{ results.get('Overturning moment for bearing capacity check', {}).get(case, 0) | round(2) }}</td>
                    <td>{{ results.get('Moment Resistance for bearing capacity check', {}).get(case, 0) | round(2) }}</td>
                    <td>{{ results.get('Bearing stress for bearing capacity check', {}).get(case, 0) | round(2) }}</td>
                    <td>{{ results.get('Net bearing capacity for bearing capacity check', {}).get(case, 0) | round(2) }}</td>
                    <td {% if results.get('CDR bearing capacity check', {}).get(case, 0) < 1 %}class="text-danger"{% else %}class="text-success"{% endif %}>
                        {{ results.get('CDR bearing capacity check', {}).get(case, 0) | round(2) }}
    </td>
    </tr>
                {% endfor %}
    <tr>
    <td>Earthquake</td>
                    <td>{{ results.get('Earthqauke case for bearing check', {}).get('EQRvbearing', 0) | round(2) }}</td>
                    <td>{{ results.get('Earthqauke case for bearing check', {}).get('EQMobearing', 0) | round(2) }}</td>
                    <td>{{ results.get('Earthqauke case for bearing check', {}).get('EQMrbearing', 0) | round(2) }}</td>
                    <td>{{ results.get('Earthqauke case for bearing check', {}).get('EQ QR', 0) | round(2) }}</td>
                    <td>{{ results.get('Earthqauke case for bearing check', {}).get('EQ Qnet', 0) | round(2) }}</td>
                    <td {% if results.get('Earthqauke case for bearing check', {}).get('CDR Bearing EQ', 0) < 1 %}class="text-danger"{% else %}class="text-success"{% endif %}>
                        {{ results.get('Earthqauke case for bearing check', {}).get('CDR Bearing EQ', 0) | round(2) }}
    </td>
    </tr>
    </tbody>
  </table>
  </div>
</div>


<div class="card mb-4">
  <div class="card-header d-flex justify-content-between align-items-center">
  <h3>Eccentricity Check</h3>
  <button class="btn btn-sm btn-outline-secondary" onclick="copyTable('eccentricityTable')">Copy</button>
  </div>
  <div class="card-body">
  <table class="table table-striped" id="eccentricityTable">
    <thead>
    <tr>
    <th>Load Case</th>
    <th>Vertical Load (kN/m)</th>
    <th>Overturning Moment (kN.m/m)</th>
    <th>Resistance Moment (kN.m/m)</th>
    <th>Eccentricity (m)</th>
    </tr>
    </thead>
    <tbody>
                {% for case in ['Maximum', 'Minimum', 'Critical'] %}
    <tr>
                    <td>{{ case }}</td>
                    <td>{{ results.get('Rv for eccentricity check', {}).get(case, 0) | round(2) }}</td>
                    <td>{{ results.get('Overturning moment for eccentricity check', {}).get(case, 0) | round(2) }}</td>
                    <td>{{ results.get('Moment Resistance for eccentricity check', {}).get(case, 0) | round(2) }}</td>
                    <td {% if results.get('Eccentricity for static check', {}).get('E' + case.lower(), 0) > results.get('Earthquake case for eccentricity check', {}).get('Allowable Eccentricity 1', 0) %}class="text-danger"{% else %}class="text-success"{% endif %}>
                        {{ results.get('Eccentricity for static check', {}).get('E' + case.lower(), 0) | round(4) }}
    </td>
    </tr>
                {% endfor %}
    <tr>
    <td>Earthquake</td>
                    <td>{{ results.get('Earthquake case for eccentricity check', {}).get('EQRv', 0) | round(2) }}</td>
                    <td>{{ results.get('Earthquake case for eccentricity check', {}).get('EQMo', 0) | round(2) }}</td>
                    <td>{{ results.get('Earthquake case for eccentricity check', {}).get('EQMrec', 0) | round(2) }}</td>
                    <td {% if results.get('Earthquake case for eccentricity check', {}).get('EQ Eccentricity', 0) > results.get('Earthquake case for eccentricity check', {}).get('Allowable EQ Eccentricity', 0) %}class="text-danger"{% else %}class="text-success"{% endif %}>
                        {{ results.get('Earthquake case for eccentricity check', {}).get('EQ Eccentricity', 0) | round(4) }}
    </td>
    </tr>
    </tbody>
  </table>
  </div>
</div>


<div class="alert alert-info">
  <strong>Note:</strong> CDR (Capacity Demand Ratio) is defined as the factored resistance divided by the factored force. In limit states, it should be more than one to ensure safety.
</div>


<div class="mt-4">
  <button class="btn btn-secondary" id="back-button">
  <i class="fas fa-arrow-left"></i> Back
  </button>
</div>

<script>
function copyTable(tableId) {
  const table = document.getElementById(tableId);
  const range = document.createRange();
  range.selectNode(table);
  window.getSelection().removeAllRanges();
  window.getSelection().addRange(range);
  document.execCommand('copy');
  window.getSelection().removeAllRanges();
  alert('Table copied to clipboard!');
}

const backButton = document.getElementById('back-button');
if (backButton) {
  backButton.addEventListener('click', function() {
  window.history.back();
  });
}
</script>
{% endblock %}
/**
 * Performance Utilities Module
 * Provides common performance optimization utilities for the GRS Application
 * @version 1.0.0 - Performance Optimized
 */

(function() {
    'use strict';

    // Global performance utilities namespace
    window.PerformanceUtils = {
        
        /**
         * Debounce utility - Delays function execution until after wait milliseconds
         * @param {Function} func - Function to debounce
         * @param {number} wait - Milliseconds to wait
         * @returns {Function} Debounced function
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func.apply(this, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        /**
         * Throttle utility - Limits function execution to once per wait milliseconds
         * @param {Function} func - Function to throttle
         * @param {number} wait - Milliseconds to wait between executions
         * @returns {Function} Throttled function
         */
        throttle: function(func, wait) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, wait);
                }
            };
        },

        /**
         * DOM Element Cache - Caches DOM elements to avoid repeated queries
         */
        DOMCache: class {
            constructor() {
                this.cache = new Map();
            }

            get(id) {
                if (!this.cache.has(id)) {
                    this.cache.set(id, document.getElementById(id));
                }
                return this.cache.get(id);
            }

            clear() {
                this.cache.clear();
            }

            delete(id) {
                this.cache.delete(id);
            }
        },

        /**
         * Event Listener Manager - Tracks and manages event listeners for cleanup
         */
        EventManager: class {
            constructor() {
                this.listeners = [];
                this.timeouts = [];
                this.intervals = [];
            }

            addEventListener(element, event, handler, options) {
                element.addEventListener(event, handler, options);
                this.listeners.push({ element, event, handler, options });
            }

            setTimeout(callback, delay) {
                const id = setTimeout(callback, delay);
                this.timeouts.push(id);
                return id;
            }

            setInterval(callback, delay) {
                const id = setInterval(callback, delay);
                this.intervals.push(id);
                return id;
            }

            cleanup() {
                // Remove all tracked event listeners
                this.listeners.forEach(({ element, event, handler, options }) => {
                    if (element && element.removeEventListener) {
                        element.removeEventListener(event, handler, options);
                    }
                });
                this.listeners = [];

                // Clear all tracked timeouts
                this.timeouts.forEach(id => clearTimeout(id));
                this.timeouts = [];

                // Clear all tracked intervals
                this.intervals.forEach(id => clearInterval(id));
                this.intervals = [];
            }
        },

        /**
         * Performance Monitor - Tracks performance metrics
         */
        Monitor: {
            timers: new Map(),

            start: function(label) {
                this.timers.set(label, performance.now());
            },

            end: function(label) {
                const startTime = this.timers.get(label);
                if (startTime) {
                    const duration = performance.now() - startTime;
                    if (window.location.hostname === 'localhost') {
                        console.log(`⚡ Performance [${label}]: ${duration.toFixed(2)}ms`);
                    }
                    this.timers.delete(label);
                    return duration;
                }
                return null;
            },

            measure: function(label, fn) {
                this.start(label);
                const result = fn();
                this.end(label);
                return result;
            }
        },

        /**
         * Memory Usage Monitor - Tracks memory usage (if available)
         */
        MemoryMonitor: {
            log: function(label) {
                if (performance.memory && window.location.hostname === 'localhost') {
                    const memory = performance.memory;
                    console.log(`🧠 Memory [${label}]:`, {
                        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
                        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
                        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`
                    });
                }
            }
        },

        /**
         * Batch DOM Operations - Reduces reflows and repaints
         */
        batchDOMOperations: function(operations) {
            // Use requestAnimationFrame to batch DOM operations
            return new Promise(resolve => {
                requestAnimationFrame(() => {
                    operations();
                    resolve();
                });
            });
        },

        /**
         * Lazy Loading Utility - Loads content when needed
         */
        lazyLoad: function(selector, loadFunction) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        loadFunction(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            });

            document.querySelectorAll(selector).forEach(el => {
                observer.observe(el);
            });

            return observer;
        },

        /**
         * Canvas Performance Utilities
         */
        Canvas: {
            /**
             * Optimized canvas clearing
             */
            clearCanvas: function(canvas, ctx) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            },

            /**
             * Batch canvas operations for better performance
             */
            batchOperations: function(ctx, operations) {
                ctx.save();
                operations(ctx);
                ctx.restore();
            },

            /**
             * Debounced canvas redraw
             */
            createDebouncedRedraw: function(redrawFunction, delay = 150) {
                return window.PerformanceUtils.debounce(redrawFunction, delay);
            }
        },

        /**
         * LocalStorage Performance Utilities
         */
        Storage: {
            /**
             * Batch localStorage operations
             */
            batchSet: function(items) {
                Object.entries(items).forEach(([key, value]) => {
                    localStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value));
                });
            },

            /**
             * Batch localStorage reads
             */
            batchGet: function(keys) {
                const result = {};
                keys.forEach(key => {
                    const value = localStorage.getItem(key);
                    try {
                        result[key] = value ? JSON.parse(value) : null;
                    } catch (e) {
                        result[key] = value;
                    }
                });
                return result;
            }
        }
    };

    // Initialize global performance monitoring
    if (window.location.hostname === 'localhost') {
        console.log('⚡ Performance utilities loaded');
        window.PerformanceUtils.MemoryMonitor.log('Initial');
    }

})();

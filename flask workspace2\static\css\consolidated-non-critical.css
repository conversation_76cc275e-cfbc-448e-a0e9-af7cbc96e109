/**
 * Non-Critical CSS - Below the fold and component-specific styles
 * This file is loaded asynchronously to avoid render blocking
 * @version 1.0.0 - Performance Optimized
 */

/* Sidebar Styles */
.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.sidebar-brand {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
}

.sidebar-nav {
  padding: var(--spacing-md) 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem var(--spacing-lg);
  color: var(--gray-600);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
}

.menu-item:hover {
  background-color: var(--gray-50);
  color: var(--primary-color);
  text-decoration: none;
}

.menu-item.active {
  background-color: var(--primary-color);
  color: white;
  border-left-color: var(--primary-dark);
}

.menu-item i {
  width: 20px;
  margin-right: var(--spacing-md);
  text-align: center;
}

.user-info {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.user-name {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-xs);
}

.user-role {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.logout-btn {
  width: 100%;
  margin-top: var(--spacing-md);
  background-color: var(--danger-color);
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.logout-btn:hover {
  background-color: #dc2626;
}

/* Page Title */
.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-800);
  margin: 0;
}

/* Cards */
.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: box-shadow var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  font-weight: 600;
  color: var(--gray-800);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
}

/* Tables */
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--gray-700);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid var(--gray-200);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--gray-200);
  font-weight: 600;
  color: var(--gray-800);
  background-color: var(--gray-50);
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Alerts */
.alert {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
}

.alert-success {
  color: #065f46;
  background-color: #d1fae5;
  border-color: #a7f3d0;
}

.alert-danger {
  color: #991b1b;
  background-color: #fee2e2;
  border-color: #fecaca;
}

.alert-warning {
  color: #92400e;
  background-color: #fef3c7;
  border-color: #fde68a;
}

.alert-info {
  color: #1e40af;
  background-color: #dbeafe;
  border-color: #93c5fd;
}

/* Modals/Popups */
.grs-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.grs-popup.grs-popup-show {
  opacity: 1;
  visibility: visible;
}

.grs-popup-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.grs-popup-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
}

.grs-popup-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.grs-popup-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.grs-popup-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--gray-400);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.grs-popup-close:hover {
  background-color: var(--gray-100);
  color: var(--gray-600);
}

.grs-popup-body {
  padding: var(--spacing-lg);
}

.grs-popup-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

/* Canvas Containers */
.canvas-container {
  position: relative;
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
  margin: var(--spacing-lg) 0;
}

.canvas-controls {
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

/* Form Enhancements */
.form-row {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.form-col {
  flex: 1;
}

.form-help {
  font-size: 0.75rem;
  color: var(--gray-500);
  margin-top: var(--spacing-xs);
}

.form-error {
  font-size: 0.75rem;
  color: var(--danger-color);
  margin-top: var(--spacing-xs);
}

.form-success {
  font-size: 0.75rem;
  color: var(--success-color);
  margin-top: var(--spacing-xs);
}

/* Button Variants - Extended System */
.btn-secondary {
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
  color: var(--gray-700);
  border-color: var(--gray-300);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--gray-200), var(--gray-300));
  border-color: var(--gray-400);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), #16a34a);
  color: white;
  border-color: var(--success-color);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, #16a34a, #15803d);
  border-color: #16a34a;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  color: white;
  border-color: var(--danger-color);
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-color: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Button Sizes */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  min-height: 36px;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
  min-height: 52px;
}

/* Button Groups */
.button-group, .button-row {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 1.25rem;
}

.button-group .btn:first-child {
  margin-left: 0;
}

.button-group .btn:last-child {
  margin-right: 0;
}

/* Tab Buttons - Special Style */
.tab-button {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border: 1px solid var(--primary-color);
  padding: 0.75rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.tab-button:hover:not(.active) {
  background: linear-gradient(135deg, var(--primary-dark), #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.2);
}

.tab-button.active {
  background: linear-gradient(135deg, var(--primary-dark), #1e40af);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(37, 99, 235, 0.3);
  transform: translateY(1px);
}

/* Save Button - Special Styling */
#save-button {
  background: linear-gradient(135deg, var(--success-color), #16a34a);
  color: white;
  border: 1px solid var(--success-color);
  padding: 0.875rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
  margin-top: 1.5rem;
}

#save-button:hover {
  background: linear-gradient(135deg, #16a34a, #15803d);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.3);
}

#save-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

/* Visualization Control Buttons */
.visualization-section .button-group {
  justify-content: center;
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.visualization-section .btn {
  min-width: 120px;
}

#screenshot-button {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  border-color: #8b5cf6;
}

#screenshot-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
  border-color: #7c3aed;
}

/* Remove Row Buttons */
.btn-remove, .btn-remove-layout-row {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  min-height: 32px;
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

.btn-remove:hover, .btn-remove-layout-row:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* Add Row Buttons */
.btn-add, #add-row, #addReinforcementLayoutRowBtn {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  border: 1px solid var(--primary-color);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.btn-add:hover, #add-row:hover, #addReinforcementLayoutRowBtn:hover {
  background: linear-gradient(135deg, var(--primary-dark), #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Mobile Responsive Button Styles */
@media (max-width: 768px) {
  .btn {
    padding: 0.875rem 1.25rem;
    font-size: 1rem;
    min-height: 48px;
  }

  .btn-sm {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    min-height: 40px;
  }

  .button-group, .button-row {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .button-group .btn, .button-row .btn {
    width: 100%;
    margin: 0;
  }

  .tab-button {
    padding: 1rem;
    font-size: 1rem;
    min-height: 48px;
  }

  #save-button {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    min-height: 52px;
    width: 100%;
  }

  .visualization-section .button-group {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }

  .visualization-section .btn {
    flex: 1;
    min-width: 100px;
    max-width: 140px;
  }
}

@media (max-width: 480px) {
  .btn {
    padding: 1rem;
    font-size: 1rem;
    min-height: 50px;
  }

  .visualization-section .button-group {
    flex-direction: column;
  }

  .visualization-section .btn {
    width: 100%;
    max-width: none;
  }

  .tab-button {
    padding: 1.125rem;
    font-size: 1.125rem;
    min-height: 52px;
  }
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn-warning:hover {
  background-color: #d97706;
  border-color: #d97706;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

.status-success {
  color: var(--success-color);
  background-color: #dcfce7;
}

.status-error {
  color: var(--danger-color);
  background-color: #fee2e2;
}

.status-warning {
  color: var(--warning-color);
  background-color: #fef3c7;
}

.status-info {
  color: var(--primary-color);
  background-color: #dbeafe;
}

/* Progress Indicators */
.progress {
  width: 100%;
  height: 8px;
  background-color: var(--gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.badge-secondary {
  background-color: var(--gray-500);
  color: white;
}

.badge-success {
  background-color: var(--success-color);
  color: white;
}

.badge-danger {
  background-color: var(--danger-color);
  color: white;
}

.badge-warning {
  background-color: var(--warning-color);
  color: white;
}

/* Home Page Specific Styles */
.hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-2xl);
}

.hero h1 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: var(--spacing-lg);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero .tagline {
  font-size: 1.25rem;
  opacity: 0.9;
  margin-bottom: var(--spacing-xl);
}

.features {
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.feature {
  flex: 1;
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: transform var(--transition-fast);
}

.feature:hover {
  transform: translateY(-4px);
}

.feature-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.feature h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-md);
}

.feature p {
  color: var(--gray-600);
  line-height: 1.6;
}

/* Results Page Styles */
.results-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.result-item {
  flex: 1 1 300px;
  border: 1px solid var(--gray-200);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  background-color: white;
  box-shadow: var(--shadow-sm);
}

.result-item strong {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--gray-800);
  font-weight: 600;
}

.result-item span,
.result-item pre {
  display: block;
  word-break: break-word;
  white-space: pre-wrap;
  color: var(--gray-600);
}

.result-item pre {
  background-color: var(--gray-50);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

/* Analysis Section Styles */
.analysis-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.analysis-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: var(--spacing-lg);
}

.analysis-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.analysis-card {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: transform var(--transition-fast);
}

.analysis-card:hover {
  transform: translateY(-2px);
}

.analysis-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-md);
}

.analysis-card p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-lg);
}

.cta-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--radius-md);
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  display: inline-block;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
  color: white;
  text-decoration: none;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .features {
    flex-direction: column;
  }

  .hero {
    padding: 3rem 1.5rem;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .analysis-cards {
    grid-template-columns: 1fr;
  }

  .results-container {
    flex-direction: column;
  }

  .result-item {
    flex: 1 1 auto;
  }

  .modal-dialog {
    margin: 10px;
    max-width: calc(100% - 20px);
  }

  .grs-popup-content {
    max-width: 95vw;
    margin: 10px;
  }

  .form-row {
    flex-direction: column;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .hero h1 {
    font-size: 2rem;
  }

  .feature {
    padding: var(--spacing-lg);
  }

  .analysis-header h1 {
    font-size: 2rem;
  }

  .cta-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

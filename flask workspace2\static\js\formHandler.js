/**
 * Standardized Form Handler for GRS Application
 * Provides consistent AJAX form submission and validation across all forms
 * @version 3.0.0 - Performance Optimized
 */

(function() {
    'use strict';

    // Performance optimization: DOM element cache
    const domCache = new Map();
    function getCachedElement(id) {
        if (!domCache.has(id)) {
            domCache.set(id, document.getElementById(id));
        }
        return domCache.get(id);
    }

    // Performance optimization: Debounce utility for validation
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Performance optimization: Cleanup tracking
    let eventListeners = [];
    let timeouts = [];

    function addEventListenerTracked(element, event, handler, options) {
        element.addEventListener(event, handler, options);
        eventListeners.push({ element, event, handler, options });
    }

    function cleanup() {
        // Remove all tracked event listeners
        eventListeners.forEach(({ element, event, handler, options }) => {
            if (element && element.removeEventListener) {
                element.removeEventListener(event, handler, options);
            }
        });
        eventListeners = [];

        // Clear all tracked timeouts
        timeouts.forEach(id => clearTimeout(id));
        timeouts = [];

        // Clear DOM cache
        domCache.clear();
    }

    // Make cleanup available globally for AJAX navigation
    window.formHandlerCleanup = cleanup;

    // Form configuration for different pages
    const FORM_CONFIGS = {
    'geometry-form': {
        endpoint: '/geometry',
        localStorageKey: 'geometryData',
        fields: ['wall-height', 'embedment-depth', 'wall-length', 'wall-batter', 'backslope-angle', 'backslope-rise'],
        onSuccess: function() {
            // Redraw canvas after successful save to prevent image disappearing
            if (typeof redrawCanvas === 'function') {
                setTimeout(() => redrawCanvas(), 100);
            }
        }
    },
    'reinforcedsoil-form': {
        endpoint: '/reinforcedsoil',
        localStorageKey: 'reinforcedsoilData',
        fields: ['reinforced-density', 'reinforced-friction', 'reinforced-cohesion']
    },
    'retainedsoil-form': {
        endpoint: '/retainedsoil',
        localStorageKey: 'retainedsoilData',
        fields: ['retained-density', 'retained-friction', 'retained-cohesion']
    },
    'foundationsoil-form': {
        endpoint: '/foundationsoil',
        localStorageKey: 'foundationsoilData',
        fields: ['foundationsoildensity', 'foundationsoilfriction-angle', 'foundationsoilcohesion', 'eccentricity', 'eccentricity-seismic', 'watertable']
    },

    'reinforcementproperties-form': {
        endpoint: '/reinforcementproperties',
        localStorageKey: 'reinforcementData',
        fields: ['reinforcement-type', 'tensile-strength', 'coverage-ratio']
    },
    'project-info-form': {
        endpoint: '/project_info',
        localStorageKey: 'projectInfo',
        fields: ['project-name', 'project-id', 'designer', 'client', 'description', 'date', 'revision']
    }
};

    // Initialize form handlers when DOM is ready
    document.addEventListener('DOMContentLoaded', initializeFormHandlers);

    /**
     * Initialize all configured form handlers
     * @returns {void}
     */
    function initializeFormHandlers() {
        try {
            // Initialize each configured form
            Object.keys(FORM_CONFIGS).forEach(formId => {
                const form = document.getElementById(formId);
                if (form) {
                    // Remove existing handler flag to allow reinitialization
                    form.removeAttribute('data-form-handler-attached');
                    setupFormHandler(form, FORM_CONFIGS[formId]);
                }
            });
        } catch (error) {
            console.error('Error initializing form handlers:', error);
        }
    }

    /**
     * Setup form handler for a specific form
     * @param {HTMLFormElement} form - The form element
     * @param {Object} config - Form configuration
     * @returns {void}
     */
    function setupFormHandler(form, config) {
        try {
            // Don't interfere with forms that have special handling
            const specialForms = ['reinforcementlayout-form', 'externalloadsform', 'reinforcementproperties-form'];
            if (specialForms.includes(form.id)) {
                return;
            }

            // Load saved data from localStorage
            loadFormData(form, config);

            // Add real-time validation (but don't replace existing listeners)
            addValidationListeners(form);

            // Only add form submission handler if one doesn't already exist
            if (!form.hasAttribute('data-form-handler-attached')) {
                form.addEventListener('submit', function(event) {
                    event.preventDefault();
                    event.stopPropagation();
                    event.stopImmediatePropagation();
                    handleFormSubmission(form, config);
                }, true); // Use capture mode to ensure this runs first
                form.setAttribute('data-form-handler-attached', 'true');
            }
        } catch (error) {
            console.error(`Error setting up form handler for ${form.id}:`, error);
        }
    }

    /**
     * Load saved form data from localStorage
     * @param {HTMLFormElement} form - The form element
     * @param {Object} config - Form configuration
     * @returns {void}
     */
    function loadFormData(form, config) {
        if (!config.localStorageKey) return;

        try {
            const savedData = JSON.parse(localStorage.getItem(config.localStorageKey));
            if (!savedData) return;

            // Special handling for geometry form - map localStorage keys to form field IDs
            if (form.id === 'geometry-form') {
                const fieldMapping = {
                    'wallHeight': 'wall-height',
                    'embedmentDepth': 'embedment-depth',
                    'wallLength': 'wall-length',
                    'wallBatter': 'wall-batter',
                    'backslopeAngle': 'backslope-angle',
                    'backslopeRise': 'backslope-rise'
                };

                Object.keys(fieldMapping).forEach(dataKey => {
                    const fieldId = fieldMapping[dataKey];
                    const field = form.querySelector(`#${fieldId}`);
                    if (field && savedData[dataKey] !== undefined) {
                        field.value = savedData[dataKey];
                    }
                });
            } else {
                // Standard handling for other forms
                config.fields.forEach(fieldId => {
                    const field = form.querySelector(`#${fieldId}`);
                    if (field && savedData[fieldId] !== undefined) {
                        field.value = savedData[fieldId];
                    }
                });
            }
        } catch (error) {
            console.error(`Error loading data for ${form.id}:`, error);
        }
    }

    /**
     * Add validation listeners to form inputs
     * @param {HTMLFormElement} form - The form element
     * @returns {void}
     */
    // Performance optimization: Debounced validation
    const debouncedValidateField = debounce((field) => {
        validateField(field);
    }, 300);

    function addValidationListeners(form) {
        // Skip adding input listeners for forms that have their own dynamic updating
        const formsWithOwnUpdating = ['geometry-form', 'externalloadsform', 'reinforcementlayout-form', 'reinforcementproperties-form'];
        if (formsWithOwnUpdating.includes(form.id)) {
            return;
        }

        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            // Only add listeners if not already added
            if (!input.hasAttribute('data-validation-attached')) {
                // Performance optimization: Use tracked event listeners and debounced validation
                addEventListenerTracked(input, 'input', () => debouncedValidateField(input));
                addEventListenerTracked(input, 'blur', () => validateField(input)); // Immediate validation on blur
                input.setAttribute('data-validation-attached', 'true');
            }
        });
    }

    /**
     * Validate a single form field
     * @param {HTMLInputElement} field - The field to validate
     * @returns {boolean} - Whether the field is valid
     */
    function validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Required validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        }

        // Numeric validation
        if (field.type === 'number' && value) {
            if (isNaN(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid number';
            } else {
                const numValue = parseFloat(value);
                if (field.min && numValue < parseFloat(field.min)) {
                    isValid = false;
                    errorMessage = `Value must be at least ${field.min}`;
                }
                if (field.max && numValue > parseFloat(field.max)) {
                    isValid = false;
                    errorMessage = `Value must be at most ${field.max}`;
                }
            }
        }

        // Update field styling
        field.classList.toggle('is-invalid', !isValid);
        field.classList.toggle('invalid', !isValid); // Alternative class name
        field.classList.toggle('is-valid', isValid && value);

        // Show/hide error message
        let errorElement = field.parentNode.querySelector('.invalid-feedback, .error-message');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            field.parentNode.appendChild(errorElement);
        }

        errorElement.textContent = errorMessage;
        errorElement.style.display = errorMessage ? 'block' : 'none';

        return isValid;
    }

    /**
     * Validate entire form
     * @param {HTMLFormElement} form - The form to validate
     * @returns {boolean} - Whether the form is valid
     */
    function validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * Handle form submission with AJAX
     * @param {HTMLFormElement} form - The form to submit
     * @param {Object} config - Form configuration
     * @returns {void}
     */
    function handleFormSubmission(form, config) {
        // Validate form
        if (!validateForm(form)) {
            showErrorPopup('Please fix the errors in the form before submitting.');
            return;
        }

        // Prepare form data
        const formData = new FormData(form);

        // Show loading state
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        const originalText = submitButton ? submitButton.textContent : '';
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Saving...';
        }
    
    // Submit form
    fetch(config.endpoint, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        // Save to localStorage
        if (config.localStorageKey) {
            const formObject = {};

            // Special handling for geometry form - use camelCase keys for localStorage
            if (form.id === 'geometry-form') {
                const fieldMapping = {
                    'wall-height': 'wallHeight',
                    'embedment-depth': 'embedmentDepth',
                    'wall-length': 'wallLength',
                    'wall-batter': 'wallBatter',
                    'backslope-angle': 'backslopeAngle',
                    'backslope-rise': 'backslopeRise'
                };

                Object.keys(fieldMapping).forEach(fieldId => {
                    const field = form.querySelector(`#${fieldId}`);
                    if (field) {
                        const dataKey = fieldMapping[fieldId];
                        formObject[dataKey] = parseFloat(field.value) || 0;
                    }
                });
            } else {
                // Standard handling for other forms
                config.fields.forEach(fieldId => {
                    const field = form.querySelector(`#${fieldId}`);
                    if (field) {
                        formObject[fieldId] = field.value;
                    }
                });
            }

            localStorage.setItem(config.localStorageKey, JSON.stringify(formObject));
        }
        
            // Show success message
            if (data.message) {
                showSuccessPopup(data.message);
                // Update sidebar status indicator
                updateSidebarStatus(form.id);
            }

            // Call success callback
            if (config.onSuccess) {
                config.onSuccess(data);
            }
    })
    .catch(error => {
        console.error(`Error submitting ${form.id}:`, error);
        showErrorPopup('Error saving data: ' + error.message);
    })
    .finally(() => {
        // Restore button state
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    });
}

    // Popup/Modal Functions
    /**
     * Show success popup
     * @param {string} message - Success message to display
     * @returns {void}
     */
    function showSuccessPopup(message) {
        showPopup(message, 'success');
    }

    /**
     * Show error popup
     * @param {string} message - Error message to display
     * @returns {void}
     */
    function showErrorPopup(message) {
        showPopup(message, 'error');
    }

    /**
     * Show popup with specified message and type
     * @param {string} message - Message to display
     * @param {string} type - Popup type ('success' or 'error')
     * @returns {void}
     */
    function showPopup(message, type = 'success') {
        try {
            // Remove any existing popup
            const existingPopup = document.getElementById('grs-popup');
            if (existingPopup) {
                existingPopup.remove();
            }

            // Create popup HTML
            const popup = document.createElement('div');
            popup.id = 'grs-popup';
            popup.className = `grs-popup grs-popup-${type}`;

            popup.innerHTML = `
                <div class="grs-popup-overlay" onclick="closePopup()"></div>
                <div class="grs-popup-content">
                    <div class="grs-popup-header">
                        <h3>${type === 'success' ? '✅ Success' : '❌ Error'}</h3>
                        <button class="grs-popup-close" onclick="closePopup()">&times;</button>
                    </div>
                    <div class="grs-popup-body">
                        <p>${message}</p>
                    </div>
                    <div class="grs-popup-footer">
                        <button class="grs-popup-btn grs-popup-btn-primary" onclick="closePopup()">OK</button>
                    </div>
                </div>
            `;

            // Add to document
            document.body.appendChild(popup);

            // Show popup with animation
            setTimeout(() => {
                popup.classList.add('grs-popup-show');
            }, 10);

            // Auto-close after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(closePopup, 5000);
            }
        } catch (error) {
            console.error('Error showing popup:', error);
        }
    }

    /**
     * Close the current popup
     * @returns {void}
     */
    function closePopup() {
        const popup = document.getElementById('grs-popup');
        if (popup) {
            popup.classList.remove('grs-popup-show');
            setTimeout(() => {
                popup.remove();
            }, 300);
        }
    }

    /**
     * Update sidebar status indicator
     * @param {string} formId - Form ID to update status for
     * @returns {void}
     */
    function updateSidebarStatus(formId) {
        try {
            const formToRouteMap = {
                'geometry-form': '/geometry',
                'reinforcedsoil-form': '/reinforcedsoil',
                'retainedsoil-form': '/retainedsoil',
                'foundationsoil-form': '/foundationsoil',
                'externalloadsform': '/externalloads',
                'reinforcementproperties-form': '/reinforcementproperties',
                'reinforcementlayout-form': '/reinforcementlayout',
                'project-info-form': '/project_info'
            };

            const route = formToRouteMap[formId];
            if (!route) return;

            const menuLink = document.querySelector(`a[href="${route}"]`);
            if (!menuLink) return;

            // Check if status icon already exists
            let statusIcon = menuLink.querySelector('.status-icon');
            if (!statusIcon) {
                // Create new status icon
                statusIcon = document.createElement('i');
                statusIcon.className = 'fas fa-check-circle status-icon saved';
                menuLink.appendChild(statusIcon);
            } else {
                // Update existing icon
                statusIcon.className = 'fas fa-check-circle status-icon saved';
            }
        } catch (error) {
            console.error('Error updating sidebar status:', error);
        }
    }

    // Public API - expose functions globally for AJAX reinitialization
    window.initializeFormHandlers = initializeFormHandlers;
    window.setupFormHandler = setupFormHandler;
    window.handleFormSubmission = handleFormSubmission;
    window.FORM_CONFIGS = FORM_CONFIGS;
    window.showSuccessPopup = showSuccessPopup;
    window.showErrorPopup = showErrorPopup;
    window.showPopup = showPopup;
    window.closePopup = closePopup;
    window.updateSidebarStatus = updateSidebarStatus;

})();

// Production ready - only log in development
if (typeof window !== 'undefined' && window.location && window.location.hostname === 'localhost') {
    console.log('✅ Form Handler loaded (development mode)');
}

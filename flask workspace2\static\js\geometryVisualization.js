/**
 * Geometry Visualization Module
 * Handles geometry canvas drawing and interactions
 * @version 3.0.0 - Performance Optimized
 */

(function() {
    'use strict';

    // Check if DrawingUtils is available
    if (typeof window.DrawingUtils === 'undefined') {
        console.error('DrawingUtils is required but not loaded. Please ensure drawingUtils.js is loaded first.');
        return;
    }

    // Performance optimization: Debounce utility
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Performance optimization: DOM element cache
    const domCache = new Map();
    function getCachedElement(id) {
        if (!domCache.has(id)) {
            domCache.set(id, document.getElementById(id));
        }
        return domCache.get(id);
    }

    // Performance optimization: Clear DOM cache when needed
    function clearDOMCache() {
        domCache.clear();
    }

    // Performance optimization: Cleanup function for memory leak prevention
    let eventListeners = [];
    let timeouts = [];
    let intervals = [];

    function addEventListenerTracked(element, event, handler, options) {
        element.addEventListener(event, handler, options);
        eventListeners.push({ element, event, handler, options });
    }

    function setTimeoutTracked(callback, delay) {
        const id = setTimeout(callback, delay);
        timeouts.push(id);
        return id;
    }

    function cleanup() {
        // Remove all tracked event listeners
        eventListeners.forEach(({ element, event, handler, options }) => {
            if (element && element.removeEventListener) {
                element.removeEventListener(event, handler, options);
            }
        });
        eventListeners = [];

        // Clear all tracked timeouts
        timeouts.forEach(id => clearTimeout(id));
        timeouts = [];

        // Clear all tracked intervals
        intervals.forEach(id => clearInterval(id));
        intervals = [];

        // Clear DOM cache
        clearDOMCache();

        // Reset initialization flag
        window.geometryVisualizationInitialized = false;
    }

    // Make cleanup available globally for AJAX navigation
    window.geometryVisualizationCleanup = cleanup;

    /**
     * Initialize Geometry Visualization
     * @returns {boolean} Success status
     */
    function initializeGeometryVisualization() {
        // Prevent multiple initializations
        if (window.geometryVisualizationInitialized) {
            return false;
        }

        try {

            // Performance optimization: Use cached DOM elements
            const canvas = getCachedElement('geometry-canvas');
            if (!canvas) {
                console.error('Canvas geometry-canvas not found!');
                return false;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('Failed to get canvas context');
                return false;
            }

            // Canvas state variables
            let scale = 1;
            let translateX = 0;
            let translateY = 0;
            let isDown = false;
            let lastX, lastY;

            canvas.width = 1000;
            canvas.height = 600;

            /**
             * Get current form values for geometry
             * @returns {Object} Current form values
             */
            function getFormValues() {
                return {
                    wallHeight: parseFloat(document.getElementById('wall-height')?.value) || 5,
                    embedmentDepth: parseFloat(document.getElementById('embedment-depth')?.value) || 1,
                    wallLength: parseFloat(document.getElementById('wall-length')?.value) || 6,
                    wallBatter: parseFloat(document.getElementById('wall-batter')?.value) || 0,
                    backslopeAngle: parseFloat(document.getElementById('backslope-angle')?.value) || 0,
                    backslopeRise: parseFloat(document.getElementById('backslope-rise')?.value) || 0
                };
            }

            /**
             * Main drawing function - restored original functionality
             * @param {number} wallHeight - Wall height in meters
             * @param {number} embedmentDepth - Embedment depth in meters
             * @param {number} wallLength - Wall length in meters
             * @param {number} wallBatter - Wall batter in degrees
             * @param {number} backslopeAngle - Backslope angle in degrees
             * @param {number} backslopeRise - Backslope rise in meters
             * @returns {void}
             */
            function drawGRSWall(wallHeight, embedmentDepth, wallLength, wallBatter, backslopeAngle, backslopeRise) {
                try {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    ctx.save();
                    ctx.translate(translateX, translateY);
                    ctx.scale(scale, scale);

                    const baseScale = 50;
                    const baseX = 100, baseY = 400;
                    const batterOffset = Math.tan((wallBatter * Math.PI) / 180) * (wallHeight * baseScale);
                    const fasciaThickness = 0.2 * baseScale;

                    // Define geometry components
                    const fascia = {
                        x1: baseX - fasciaThickness, y1: baseY,
                        x2: baseX - fasciaThickness + batterOffset, y2: baseY - (wallHeight * baseScale),
                        x3: baseX + batterOffset + fasciaThickness, y3: baseY - (wallHeight * baseScale),
                        x4: baseX, y4: baseY
                    };

                    const reinforcedFill = {
                        x1: baseX, y1: baseY,
                        x2: baseX + batterOffset, y2: baseY - (wallHeight * baseScale),
                        x3: baseX + (wallLength * baseScale) + batterOffset, y3: baseY - (wallHeight * baseScale),
                        x4: baseX + (wallLength * baseScale), y4: baseY
                    };

                    const retainedFill = {
                        x1: baseX + (wallLength * baseScale), y1: baseY,
                        x2: baseX + (wallLength * baseScale) + batterOffset, y2: baseY - (wallHeight * baseScale),
                        x3: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y3: baseY - (wallHeight * baseScale),
                        x4: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale), y4: baseY
                    };

                    const embedment = {
                        x1: baseX - 10 - (1 * wallHeight * baseScale), y1: baseY,
                        x2: baseX - 10 + batterOffset, y2: baseY - (embedmentDepth * baseScale)
                    };

                    const foundationSoil = {
                        x1: embedment.x1, y1: baseY + (0.5*wallHeight * baseScale),
                        x2: retainedFill.x4, y2: baseY
                    };

                    // Calculate backslope
                    const backslopeAngleRadians = (backslopeAngle * Math.PI) / 180;
                    const slopeBaseLength = backslopeRise / Math.tan(backslopeAngleRadians);
                    const slopeStartX = reinforcedFill.x2;
                    const slopeStartY = reinforcedFill.y2;
                    let slopeEndX = slopeStartX + (slopeBaseLength * baseScale);
                    let slopeEndY = slopeStartY - (backslopeRise * baseScale);

                    // Draw backslope based on geometry
                    ctx.fillStyle = "#FFA500";

                    if (slopeEndX <= retainedFill.x3) {
                        // Case 1: Slope ends before retained fill
                        const horizontalEndX = retainedFill.x3;
                        const horizontalEndY = slopeEndY;

                        // Draw slope line
                        ctx.beginPath();
                        ctx.moveTo(slopeStartX, slopeStartY);
                        ctx.lineTo(slopeEndX, slopeEndY);
                        ctx.stroke();

                        // Draw horizontal extension
                        ctx.beginPath();
                        ctx.moveTo(slopeEndX, slopeEndY);
                        ctx.lineTo(horizontalEndX, horizontalEndY);
                        ctx.stroke();

                        // Fill area
                        ctx.beginPath();
                        ctx.moveTo(slopeStartX, slopeStartY);
                        ctx.lineTo(slopeEndX, slopeEndY);
                        ctx.lineTo(horizontalEndX, horizontalEndY);
                        ctx.lineTo(retainedFill.x3, retainedFill.y3);
                        ctx.closePath();
                        ctx.fill();

                        // Display rise dimension
                        const topRise = backslopeRise;
                        ctx.beginPath();
                        ctx.moveTo(retainedFill.x3, retainedFill.y3);
                        ctx.lineTo(retainedFill.x3, retainedFill.y3 - topRise * baseScale);
                        ctx.stroke();

                        // Draw dimension arrow and text
                        window.DrawingUtils.drawArrow(ctx, retainedFill.x3, retainedFill.y3, retainedFill.x3, retainedFill.y3 - topRise * baseScale, 0.1);
                        ctx.font = "18px Arial";
                        ctx.fillStyle = "#000";
                        ctx.textAlign = "center";
                        ctx.fillText(`${topRise.toFixed(2)} m`, retainedFill.x3 -40, retainedFill.y3 - (topRise * baseScale) / 2);
                    } else {
                        // Case 2: Slope extends beyond retained fill
                        slopeEndX = retainedFill.x3;
                        slopeEndY = slopeStartY - ((slopeEndX - slopeStartX) * Math.tan(backslopeAngleRadians));

                        // Draw slope line
                        ctx.beginPath();
                        ctx.moveTo(slopeStartX, slopeStartY);
                        ctx.lineTo(slopeEndX, slopeEndY);
                        ctx.stroke();

                        // Fill area
                        ctx.beginPath();
                        ctx.moveTo(slopeStartX, slopeStartY);
                        ctx.lineTo(slopeEndX, slopeEndY);
                        ctx.lineTo(retainedFill.x3, retainedFill.y3);
                        ctx.closePath();
                        ctx.fill();

                        // Display rise dimension
                        const topRise = (retainedFill.x3 - reinforcedFill.x2) * Math.tan(backslopeAngleRadians)/baseScale;
                        ctx.beginPath();
                        ctx.moveTo(retainedFill.x3, retainedFill.y3);
                        ctx.lineTo(retainedFill.x3, retainedFill.y3 - topRise*baseScale);
                        ctx.stroke();

                        // Draw dimension arrow and text
                        window.DrawingUtils.drawArrow(ctx, retainedFill.x3, retainedFill.y3, retainedFill.x3, retainedFill.y3 - (topRise*baseScale), 0.1);
                        ctx.font = "18px Arial";
                        ctx.fillStyle = "#000";
                        ctx.textAlign = "center";
                        ctx.fillText(`${topRise.toFixed(2)} m`, retainedFill.x3 - 40, retainedFill.y3 - (topRise*0.5*baseScale));
                    }

                    // Draw foundation soil
                    ctx.fillStyle = "#A98B6D";
                    ctx.fillRect(foundationSoil.x1, foundationSoil.y1, foundationSoil.x2 - foundationSoil.x1, foundationSoil.y2 - foundationSoil.y1);

                    // Draw embedment
                    ctx.fillRect(embedment.x1, embedment.y1, embedment.x2 - embedment.x1, embedment.y2 - embedment.y1);

                    // Draw fascia
                    ctx.fillStyle = "#666";
                    ctx.beginPath();
                    ctx.moveTo(fascia.x1, fascia.y1);
                    ctx.lineTo(fascia.x2, fascia.y2);
                    ctx.lineTo(fascia.x3, fascia.y3);
                    ctx.lineTo(fascia.x4, fascia.y4);
                    ctx.fill();

                    // Draw reinforced fill
                    ctx.fillStyle = "#D6B85A";
                    ctx.beginPath();
                    ctx.moveTo(reinforcedFill.x1, reinforcedFill.y1);
                    ctx.lineTo(reinforcedFill.x2, reinforcedFill.y2);
                    ctx.lineTo(reinforcedFill.x3, reinforcedFill.y3);
                    ctx.lineTo(reinforcedFill.x4, reinforcedFill.y4);
                    ctx.fill();

                    // Draw retained fill
                    ctx.fillStyle = "#D2B48C";
                    ctx.beginPath();
                    ctx.moveTo(retainedFill.x1, retainedFill.y1);
                    ctx.lineTo(retainedFill.x2, retainedFill.y2);
                    ctx.lineTo(retainedFill.x3, retainedFill.y3);
                    ctx.lineTo(retainedFill.x4, retainedFill.y4);
                    ctx.fill();

                    // Draw dimensions with arrows
                    ctx.strokeStyle = "#000";
                    ctx.lineWidth = 1;

                    // Draw wall height dimension
                    ctx.beginPath();
                    ctx.moveTo(baseX - 20, baseY);
                    ctx.lineTo(baseX - 20, baseY - (wallHeight * baseScale));
                    ctx.stroke();
                    window.DrawingUtils.drawArrow(ctx, baseX - 20, baseY, baseX - 20, baseY - (wallHeight * baseScale), 0.05);
                    ctx.font = "18px Arial";
                    ctx.fillStyle = "#000";
                    ctx.textAlign = "center";
                    ctx.textBaseline = "top";
                    ctx.fillText(`${wallHeight} m`, baseX - 40, baseY - (0.5*wallHeight * baseScale) - 10);

                    // Draw wall length dimension
                    ctx.beginPath();
                    ctx.moveTo(baseX, baseY);
                    ctx.lineTo(baseX + (wallLength * baseScale), baseY);
                    ctx.stroke();
                    window.DrawingUtils.drawArrow(ctx, baseX, baseY, baseX + (wallLength * baseScale), baseY, 0.05);
                    ctx.font = "18px Arial";
                    ctx.fillStyle = "#000";
                    ctx.textAlign = "center";
                    ctx.textBaseline = "middle";
                    ctx.fillText(`${wallLength} m`, baseX + ((wallLength * baseScale) / 2), baseY + 10);

                    // Add labels for different sections
                    ctx.font = "18px Arial";
                    ctx.fillStyle = "#000";
                    ctx.textAlign = "center";
                    ctx.textBaseline = "middle";

                    // Reinforced fill label
                    ctx.fillText(`Reinforced Fill`, baseX + ((0.5*wallLength * baseScale)), baseY - (0.5*wallHeight * baseScale) - 10);

                    // Retained fill label
                    ctx.fillText(`Retained Fill`, baseX + ((1.5*wallLength * baseScale)), baseY - (0.5*wallHeight * baseScale) - 10);

                    // Foundation soil label
                    ctx.fillText(`Foundation Soil`, baseX + ((1.0*wallLength * baseScale)), baseY + (0.25*wallHeight * baseScale) - 10);

                    // Draw embedment depth dimension
                    ctx.beginPath();
                    ctx.moveTo(baseX - 40, baseY);
                    ctx.lineTo(baseX - 40, baseY - (embedmentDepth * baseScale));
                    ctx.stroke();
                    window.DrawingUtils.drawArrow(ctx, baseX - 40, baseY, baseX - 40, baseY - (embedmentDepth * baseScale), 0.2);
                    ctx.font = "18px Arial";
                    ctx.fillStyle = "#000";
                    ctx.textAlign = "center";
                    ctx.textBaseline = "top";
                    ctx.fillText(`${embedmentDepth} m`, baseX - 50-fasciaThickness, baseY - (0.5*embedmentDepth * baseScale) - 10);

                    ctx.restore();
                } catch (error) {
                    console.error('Error drawing GRS wall:', error);
                    ctx.restore();
                }
            }



            /**
             * Redraw canvas with current form values
             * @returns {void}
             */
            function redrawCanvas() {
                try {
                    const values = getFormValues();
                    drawGRSWall(
                        values.wallHeight,
                        values.embedmentDepth,
                        values.wallLength,
                        values.wallBatter,
                        values.backslopeAngle,
                        values.backslopeRise
                    );
                } catch (error) {
                    console.error('Error redrawing geometry canvas:', error);
                }
            }

    // Performance optimization: Debounced redraw function
    const debouncedRedraw = debounce(() => {
        if (typeof redrawCanvas === 'function') {
            redrawCanvas();
        }
    }, 150); // 150ms debounce delay

    // Define the input change handler function first
    function handleGeometryInputChange() {
        // Reduced logging for performance
        if (window.location.hostname === 'localhost') {
            console.log("Geometry input changed:", this.id, "value:", this.value);
        }

        // Trigger validation if the validateField function exists (from the template)
        if (typeof validateField === 'function') {
            validateField(this);
        }

        // Performance optimization: Use debounced redraw instead of immediate redraw
        debouncedRedraw();
    }

    // Use event delegation to handle input changes (works with AJAX content)
    function setupFormListeners() {
        if (window.location.hostname === 'localhost') {
            console.log("Setting up event delegation for geometry form...");
        }

        // Performance optimization: Use tracked event listeners for cleanup
        addEventListenerTracked(document, 'input', handleDelegatedInput);
        addEventListenerTracked(document, 'change', handleDelegatedInput);

        if (window.location.hostname === 'localhost') {
            console.log("Event delegation setup complete for geometry inputs");
        }
        return true;
    }

    // Delegated event handler
    function handleDelegatedInput(event) {
        const target = event.target;

        // Performance optimization: Early return for non-relevant events
        if (!target || target.type !== 'number') return;

        const geometryForm = getCachedElement('geometry-form');
        if (!geometryForm || !geometryForm.contains(target)) return;

        // Reduced logging for performance
        if (window.location.hostname === 'localhost') {
            console.log("Delegated geometry input changed:", target.id, "value:", target.value);
        }

        // Call the original handler with the correct context
        handleGeometryInputChange.call(target);
    }

    // Setup event delegation (always succeeds)
    setupFormListeners();

    // Make redraw function available globally for form handler integration
    window.geometryRedrawCanvas = redrawCanvas;

    // Also attach to canvas for direct access
    if (canvas) {
        canvas.redrawCanvas = redrawCanvas;
    }

    // Set up zoom controls
    const zoomInButton = document.getElementById('zoom-in-button');
    const zoomOutButton = document.getElementById('zoom-out-button');
    if (zoomInButton && zoomOutButton) {
        zoomInButton.addEventListener('click', function() {
            const cursorX = canvas.width / 2;
            const cursorY = canvas.height / 2;
            const oldScale = scale;
            scale *= 1.1;
            translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
            translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
            redrawCanvas();
        });

        zoomOutButton.addEventListener('click', function() {
            const cursorX = canvas.width / 2;
            const cursorY = canvas.height / 2;
            const oldScale = scale;
            scale *= 0.9;
            translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
            translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
            redrawCanvas();
        });
    }

    // Fit to window button
    const fitButton = document.getElementById('fit-button');
    if (fitButton) {
        fitButton.addEventListener('click', function() {
            scale = 1;
            translateX = 0;
            translateY = 0;
            redrawCanvas();
        });
    }

    // Set up canvas interactions
    canvas.addEventListener('wheel', function(e) {
        e.preventDefault();
        const rect = canvas.getBoundingClientRect();
        const cursorX = e.clientX - rect.left;
        const cursorY = e.clientY - rect.top;
        
        const oldScale = scale;
        scale *= e.deltaY > 0 ? 0.9 : 1.1;
        
        translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);
        translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);
        
        redrawCanvas();
    });

    canvas.addEventListener('mousedown', function(e) {
        isDown = true;
        canvas.style.cursor = 'grabbing';
        const rect = canvas.getBoundingClientRect();
        lastX = e.clientX - rect.left;
        lastY = e.clientY - rect.top;
    });

    canvas.addEventListener('mouseup', function() {
        isDown = false;
        canvas.style.cursor = 'grab';
    });

    canvas.addEventListener('mousemove', function(e) {
        if (isDown) {
            const rect = canvas.getBoundingClientRect();
            const currentX = e.clientX - rect.left;
            const currentY = e.clientY - rect.top;
            
            translateX += currentX - lastX;
            translateY += currentY - lastY;
            
            lastX = currentX;
            lastY = currentY;
            
            redrawCanvas();
        }
    });

            // Initial drawing - wait for form values to be loaded first
            setTimeout(() => {
                redrawCanvas();
            }, 100);

            // Additional redraw after localStorage has had time to load
            setTimeout(() => {
                redrawCanvas();
            }, 600);

            // Mark as initialized
            window.geometryVisualizationInitialized = true;
            return true;

        } catch (error) {
            console.error('Error initializing geometry visualization:', error);
            return false;
        }
    }



    /**
     * Setup screenshot functionality for geometry page
     * @returns {void}
     */
    function setupScreenshotFunctionality() {
        const screenshotButton = document.getElementById('screenshot-button');
        const geometryCanvas = document.getElementById('geometry-canvas');
        const geometryForm = document.getElementById('geometry-form');

        if (screenshotButton && geometryCanvas && geometryForm) {
            // Clear existing event listeners by cloning the button
            const newScreenshotButton = screenshotButton.cloneNode(true);
            screenshotButton.parentNode.replaceChild(newScreenshotButton, screenshotButton);

            // Add screenshot handler
            newScreenshotButton.addEventListener('click', handleGeometryScreenshot);
        }
    }

    /**
     * Handle geometry screenshot
     * @returns {void}
     */
    function handleGeometryScreenshot() {
        try {
            const geometryForm = document.getElementById('geometry-form');
            if (!geometryForm) return;

            const canvas = document.getElementById('geometry-canvas');
            if (!canvas) return;

            const dataURL = canvas.toDataURL('image/png');

            // Store screenshot on server for report generation
            storeScreenshotOnServer(dataURL);

            // Download screenshot
            const link = document.createElement('a');
            link.href = dataURL;
            link.download = 'grs_wall_geometry.png';
            link.click();
        } catch (error) {
            console.error('Error taking geometry screenshot:', error);
        }
    }

    /**
     * Store screenshot on server for report generation
     * @param {string} dataURL - Canvas data URL
     * @returns {void}
     */
    function storeScreenshotOnServer(dataURL) {
        fetch('/store-screenshot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                screenshot: dataURL
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status !== 'success') {
                console.error('Failed to store screenshot:', data.message);
            }
        })
        .catch(error => {
            console.error('Error storing screenshot:', error);
        });
    }

    // Setup screenshot functionality
    setupScreenshotFunctionality();

    /**
     * Check if all required elements are available
     * @returns {boolean} Whether elements are ready
     */
    function checkGeometryElementsReady() {
        const canvas = document.getElementById('geometry-canvas');
        const form = document.getElementById('geometry-form');
        const wallHeightInput = document.getElementById('wall-height');
        return canvas && form && wallHeightInput;
    }

    /**
     * Wait for elements and then initialize
     * @returns {void}
     */
    function waitForGeometryElements() {
        if (checkGeometryElementsReady()) {
            initializeGeometryVisualization();
        } else {
            setTimeout(waitForGeometryElements, 50);
        }
    }

    // Public API - expose initialization function globally
    window.initializeGeometryVisualization = initializeGeometryVisualization;

    // Initialize based on DOM state
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForGeometryElements);
    } else {
        waitForGeometryElements();
    }

    // Performance optimization: Single retry mechanism instead of multiple timeouts
    function retryInitialization(attempts = 0, maxAttempts = 5) {
        if (attempts >= maxAttempts) {
            if (window.location.hostname === 'localhost') {
                console.warn('Geometry visualization initialization failed after', maxAttempts, 'attempts');
            }
            return;
        }

        if (getCachedElement('geometry-canvas') && !window.geometryVisualizationInitialized) {
            initializeGeometryVisualization();
        } else if (!window.geometryVisualizationInitialized) {
            setTimeoutTracked(() => {
                retryInitialization(attempts + 1, maxAttempts);
            }, 100 * (attempts + 1)); // Progressive delay: 100ms, 200ms, 300ms, etc.
        }
    }

    // Start retry mechanism for AJAX content
    retryInitialization();

    // Fallback for window load event
    addEventListenerTracked(window, 'load', () => {
        if (getCachedElement('geometry-canvas') && !window.geometryVisualizationInitialized) {
            initializeGeometryVisualization();
        }
    });

})(); // End IIFE

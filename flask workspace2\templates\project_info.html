{% extends "base.html" %} {% block page_title %}Project Information - GRS Wall
Designer{% endblock %} {% block content %}
<h1>Project Information</h1>
<form
  id="project-info-form"
  method="POST"
  action="javascript:void(0);"
  onsubmit="return false;"
  novalidate
>
  <div class="form-group">
    <label for="project-name">Project Name:</label>
    <input type="text" id="project-name" name="project_name" required />
    <span class="error-message" id="project-name-error"></span>
  </div>

  <div class="form-group">
    <label for="project-id">Project ID:</label>
    <input
      type="text"
      id="project-id"
      name="project_id"
      required
      pattern="[A-Za-z0-9-_]+"
    />
    <span class="error-message" id="project-id-error"></span>
  </div>

  <div class="form-group">
    <label for="designer">Designer:</label>
    <input type="text" id="designer" name="designer" required />
    <span class="error-message" id="designer-error"></span>
  </div>

  <div class="form-group">
    <label for="client">Client:</label>
    <input type="text" id="client" name="client" required />
    <span class="error-message" id="client-error"></span>
  </div>

  <div class="form-group">
    <label for="description">Project Description:</label>
    <input type="text" id="description" name="description" required />
    <span class="error-message" id="description-error"></span>
  </div>

  <div class="form-group">
    <label for="date">Date (day/month/year):</label>
    <input
      type="text"
      id="date"
      name="date"
      required
      placeholder="DD/MM/YYYY"
    />
    <span class="error-message" id="date-error"></span>
  </div>

  <div class="form-group">
    <label for="revision">Revision Number:</label>
    <input
      type="number"
      id="revision"
      name="revision"
      required
      min="0"
      step="1"
    />
    <span class="error-message" id="revision-error"></span>
  </div>

  <div class="form-actions">
    <button type="submit" id="save-button">
      <i class="fas fa-save"></i>
      Save Project Info
    </button>
  </div>
</form>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const form = document.getElementById("project-info-form");
    const saveButton = document.getElementById("save-button");

    // Check if required elements exist
    if (!form || !saveButton) {
      console.warn("Project info form elements not found, skipping initialization");
      return;
    }

    // Load data from localStorage
    form.querySelectorAll("input").forEach((input) => {
      const storedValue = localStorage.getItem(input.id);
      if (storedValue) input.value = storedValue;
    });

    // Validation rules
    const validations = {
      "project-name": {
        test: (value) => value.trim().length > 0,
        message: "Project name is required",
      },
      "project-id": {
        test: (value) => /^[A-Za-z0-9-_]+$/.test(value),
        message:
          "Project ID can only contain letters, numbers, hyphens, and underscores",
      },
      designer: {
        test: (value) => value.trim().length > 0,
        message: "Designer name is required",
      },
      client: {
        test: (value) => value.trim().length > 0,
        message: "Client name is required",
      },
      description: {
        test: (value) => value.trim().length > 0,
        message: "Description is required",
      },
      date: {
        test: (value) =>
          /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/.test(value),
        message: "Date must be in DD/MM/YYYY format",
      },
      revision: {
        test: (value) => value !== "" && !isNaN(value) && parseInt(value) >= 0,
        message: "Revision must be a non-negative number",
      },
    };

    // Validate individual field
    function validateField(input) {
      const fieldId = input.id;
      const errorElement = document.getElementById(`${fieldId}-error`);
      const validation = validations[fieldId];

      if (validation && !validation.test(input.value)) {
        errorElement.textContent = validation.message;
        input.classList.add("invalid");
        return false;
      } else {
        errorElement.textContent = "";
        input.classList.remove("invalid");
        return true;
      }
    }

    // Add input event listeners for real-time validation
    form.querySelectorAll("input").forEach((input) => {
      input.addEventListener("input", () => validateField(input));
    });

    // Save data to localStorage and server
    form.addEventListener("submit", function (event) {
      event.preventDefault();

      // Validate all fields
      let isValid = true;
      form.querySelectorAll("input").forEach((input) => {
        if (!validateField(input)) {
          isValid = false;
        }
      });

      if (!isValid) {
        if (typeof showErrorPopup === "function") {
          showErrorPopup(
            "Please fix the errors in the form before submitting."
          );
        } else {
          alert("Please fix the errors in the form before submitting.");
        }
        return;
      }

      // Save to localStorage
      form.querySelectorAll("input").forEach((input) => {
        localStorage.setItem(input.id, input.value);
      });

      // Save to server
      const formData = new FormData(form);
      fetch("/project_info", {
        method: "POST",
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          if (typeof showSuccessPopup === "function") {
            showSuccessPopup(
              data.message || "Project info saved successfully!"
            );
          } else {
            alert(data.message || "Project info saved successfully!");
          }

          // Update sidebar status indicator
          if (typeof updateSidebarStatus === "function") {
            updateSidebarStatus("project-info-form");
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          if (typeof showErrorPopup === "function") {
            showErrorPopup("Error saving project info.");
          } else {
            alert("Error saving project info.");
          }
        });
    });
  });
</script>
{% endblock %}

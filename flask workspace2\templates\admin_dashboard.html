{% extends "base.html" %} {% block content %}
<style>
  /* Dashboard button styling for consistent appearance */
  .dashboard-btn {
  width: 100% !important;
  height: 50px !important;
  margin-bottom: 12px !important;
  padding: 12px 20px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 10px !important;
  text-decoration: none !important;
  border-radius: 6px !important;
  transition: all 0.2s ease-in-out !important;
  box-sizing: border-box !important;
  border: 2px solid !important;
  }

  .dashboard-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  text-decoration: none !important;
  }

  .dashboard-btn i {
  font-size: 16px;
  width: 20px;
  text-align: center;
  }

  /* Specific button color schemes */
  .btn-primary-custom {
  background-color: #007bff !important;
  border-color: #007bff !important;
  color: #fff !important;
  }

  .btn-primary-custom:hover {
  background-color: #0056b3 !important;
  border-color: #004085 !important;
  color: #fff !important;
  }

  .btn-success-custom {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  color: #fff !important;
  }

  .btn-success-custom:hover {
  background-color: #1e7e34 !important;
  border-color: #1c7430 !important;
  color: #fff !important;
  }

  .btn-warning-custom {
  background-color: #ffc107 !important;
  border-color: #ffc107 !important;
  color: #212529 !important;
  }

  .btn-warning-custom:hover {
  background-color: #e0a800 !important;
  border-color: #d39e00 !important;
  color: #212529 !important;
  }

  .btn-danger-custom {
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
  color: #fff !important;
  }

  .btn-danger-custom:hover {
  background-color: #c82333 !important;
  border-color: #bd2130 !important;
  color: #fff !important;
  }
</style><div class="container mt-4"><div class="jumbotron"><h1>GRS Wall Designer Administration</h1><p class="lead">
  Welcome to the administrator dashboard. From here, you can manage user
  access requests and system settings.
  </p></div><div class="row"><div class="col-md-6"><div class="card mb-4"><div class="card-header bg-primary text-white"><h4>User Management</h4></div><div class="card-body"><p>Review, approve, or reject user access requests.</p><a
            href="{{ url_for('admin_access_requests') }}"
    class="dashboard-btn btn-primary-custom"
    ><i class="fas fa-users"></i> Manage Access Requests
    </a><a
            href="{{ url_for('admin_active_sessions') }}"
    class="dashboard-btn btn-success-custom"
    ><i class="fas fa-eye"></i> View Active Sessions
    </a><button
    onclick="clearSessionData()"
    class="dashboard-btn btn-warning-custom"
    ><i class="fas fa-broom"></i> Clear Session Data
    </button><button
    onclick="clearAllSessionFiles()"
    class="dashboard-btn btn-danger-custom"
    ><i class="fas fa-trash"></i> Delete All Session Files
    </button></div></div></div><div class="col-md-6"><div class="card"><div class="card-header bg-info text-white"><h4>System Status</h4></div><div class="card-body"><p><strong>Current Time:</strong> {{ current_time }}</p><p><strong>Database Status:</strong> Connected</p><p><strong>Pending Requests:</strong> {{ pending_count }}</p></div></div></div></div></div><script>
  function clearSessionData() {
  if (
  confirm(
  "Are you sure you want to clear all session data? This will clear application data for all users but keep login sessions active."
  )
  ) {
  fetch("/admin/clear_session_data", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  })
  .then((response) => response.json())
  .then((data) => {
    if (data.status === "success") {
    if (typeof showSuccessPopup === "function") {
    showSuccessPopup("Session data cleared successfully!");
    } else {
    alert("Session data cleared successfully!");
    }
    location.reload();
    } else {
    if (typeof showErrorPopup === "function") {
    showErrorPopup("Error: " + data.message);
    } else {
    alert("Error: " + data.message);
    }
    }
  })
  .catch((error) => {
    console.error("Error:", error);
    if (typeof showErrorPopup === "function") {
    showErrorPopup("An error occurred while clearing session data.");
    } else {
    alert("An error occurred while clearing session data.");
    }
  });
  }
  }

  function clearAllSessionFiles() {
  if (
  confirm(
  "⚠️ DANGER: This will delete ALL session files from the server! All users will be logged out and lose their data. Are you absolutely sure?"
  )
  ) {
  if (
  confirm(
    "This action cannot be undone. Type 'DELETE' in the next prompt to confirm."
  )
  ) {
  const confirmation = prompt("Type 'DELETE' to confirm:");
  if (confirmation === "DELETE") {
    fetch("/admin/clear_all_session_files", {
    method: "POST",
    headers: {
    "Content-Type": "application/json",
    },
    })
    .then((response) => response.json())
    .then((data) => {
    if (data.status === "success") {
    if (typeof showSuccessPopup === "function") {
    showSuccessPopup("All session files deleted successfully!");
    } else {
    alert("All session files deleted successfully!");
    }
    location.reload();
    } else {
    if (typeof showErrorPopup === "function") {
    showErrorPopup("Error: " + data.message);
    } else {
    alert("Error: " + data.message);
    }
    }
    })
    .catch((error) => {
    console.error("Error:", error);
    if (typeof showErrorPopup === "function") {
    showErrorPopup(
    "An error occurred while deleting session files."
    );
    } else {
    alert("An error occurred while deleting session files.");
    }
    });
  } else {
    if (typeof showErrorPopup === "function") {
    showErrorPopup("Action cancelled - incorrect confirmation.");
    } else {
    alert("Action cancelled - incorrect confirmation.");
    }
  }
  }
  }
  }
</script>
{% endblock %}
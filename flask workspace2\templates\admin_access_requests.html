{% extends "base.html" %} {% block content %}
<style>
  /* Fix badge visibility issues */
  .badge-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
  }
  .badge-success {
    background-color: #28a745 !important;
    color: #ffffff !important;
  }
  .badge-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
  }

  /* Style for credentials display */
  .credentials {
    font-family: monospace;
    font-size: 0.9em;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
  }

  /* Search box styling */
  .search-container {
    margin-bottom: 20px;
  }

  .search-box {
    max-width: 400px;
  }

  /* Table styling to prevent double scrollbars */
  .table {
    margin-bottom: 0;
    width: 100%;
    table-layout: auto;
  }

  /* Table header styling to prevent wrapping */
  th {
    white-space: nowrap;
  }

  /* Button container and action button styling */
  .button-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
    margin-bottom: 30px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
  }

  .action-btn {
    min-width: 160px !important;
    height: 44px !important;
    padding: 10px 20px !important;
    font-weight: 500 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    text-decoration: none !important;
    border-radius: 6px !important;
    transition: all 0.2s ease-in-out !important;
    box-sizing: border-box !important;
    font-size: 14px !important;
  }

  .action-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    text-decoration: none !important;
  }

  .action-btn i {
    font-size: 14px;
  }

  /* Ensure consistent button styling */
  .btn-secondary.action-btn {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #fff !important;
    border: 1px solid #6c757d !important;
  }

  .btn-secondary.action-btn:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
    color: #fff !important;
    text-decoration: none !important;
  }

  /* Button container and action button styling */
  .button-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
    margin-bottom: 30px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
  }

  .action-btn {
    min-width: 160px !important;
    height: 44px !important;
    min-height: 44px !important;
    max-height: 44px !important;
    padding: 10px 20px !important;
    font-weight: 500 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    text-decoration: none !important;
    border-radius: 6px !important;
    transition: all 0.2s ease-in-out !important;
    box-sizing: border-box !important;
    font-size: 14px !important;
    line-height: 1.2 !important;
    vertical-align: top !important;
  }

  .action-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    text-decoration: none !important;
  }

  .action-btn i {
    font-size: 14px;
  }

  /* Ensure consistent button styling - Override global .btn styles */
  .btn-secondary.action-btn {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #fff !important;
    border: 1px solid #6c757d !important;
    padding: 10px 20px !important;
    height: 44px !important;
    min-height: 44px !important;
    max-height: 44px !important;
  }

  .btn-secondary.action-btn:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
    color: #fff !important;
    text-decoration: none !important;
  }
</style>

<div class="container mt-4">
  <h2>Access Requests</h2>

  {% if requests %}
  <!-- Search functionality -->
  <div class="search-container">
    <div class="input-group search-box">
      <input
        type="text"
        class="form-control"
        id="searchInput"
        placeholder="Search by name or username..."
      />
      <div class="input-group-append">
        <span class="input-group-text">
          <i class="fas fa-search"></i>
        </span>
      </div>
    </div>
  </div>

  <table class="table table-striped" id="requestsTable">
    <thead>
      <tr>
        <th>Name</th>
        <th>Email</th>
        <th>Organization</th>
        <th>Purpose</th>
        <th>Status</th>
        <th>Username</th>
        <th>Password</th>
        <th>Requested&nbsp;On</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for req in requests %}
      <tr>
        <td>{{ req[1] }}</td>
        <td>{{ req[2] }}</td>
        <td>{{ req[3] }}</td>
        <td>{{ req[4] }}</td>
        <td>
          <span
            class="badge {% if req[5] == 'pending' %}badge-warning {% elif req[5] == 'approved' %}badge-success {% else %}badge-danger{% endif %}"
          >
            {{ req[5]|title }}
          </span>
        </td>
        <td>
          {% if req[8] %}
          <span class="credentials">{{ req[8] }}</span>
          {% else %}
          <em class="text-muted">-</em>
          {% endif %}
        </td>
        <td>
          {% if req[9] %}
          <span class="credentials">{{ req[9] }}</span>
          {% else %}
          <em class="text-muted">-</em>
          {% endif %}
        </td>
        <td>{{ req[6] }}</td>
        <td>
          {% if req[5] == 'pending' %}
          <div class="btn-group" role="group">
            <button
              class="btn btn-sm btn-success approve-btn"
              data-id="{{ req[0] }}"
            >
              Approve
            </button>
            <button
              class="btn btn-sm btn-danger reject-btn"
              data-id="{{ req[0] }}"
            >
              Reject
            </button>
          </div>
          {% else %}
          <em>Processed&nbsp;on&nbsp;{{ req[7] }}</em>
          {% endif %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  {% else %}
  <div class="alert alert-info">No access requests found.</div>
  {% endif %}

  <div class="button-container mt-4">
    <a
      href="{{ url_for('admin_dashboard') }}"
      class="btn btn-secondary action-btn"
    >
      <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Check if required elements exist
    const approveButtons = document.querySelectorAll(".approve-btn");
    const rejectButtons = document.querySelectorAll(".reject-btn");

    if (!approveButtons.length && !rejectButtons.length) {
      console.warn("Admin access request buttons not found, skipping initialization");
      return;
    }

    // Handle approval button clicks
    approveButtons.forEach((button) => {
      button.addEventListener("click", function () {
        processRequest(this.dataset.id, "approve");
      });
    });

    // Handle reject button clicks
    rejectButtons.forEach((button) => {
      button.addEventListener("click", function () {
        processRequest(this.dataset.id, "reject");
      });
    });

    function processRequest(requestId, action) {
      if (!confirm(`Are you sure you want to ${action} this request?`)) return;

      const formData = new FormData();
      formData.append("action", action);

      fetch(`/admin/process_request/${requestId}`, {
        method: "POST",
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.status === "success") {
            alert(`Request ${action}d successfully!`);
            location.reload();
          } else {
            alert(`Error: ${data.message}`);
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          alert("An error occurred while processing the request.");
        });
    }

    // Search functionality
    const searchInput = document.getElementById("searchInput");
    const table = document.getElementById("requestsTable");
    const rows = table
      .getElementsByTagName("tbody")[0]
      .getElementsByTagName("tr");

    searchInput.addEventListener("keyup", function () {
      const filter = this.value.toLowerCase();

      for (let i = 0; i < rows.length; i++) {
        const nameCell = rows[i].getElementsByTagName("td")[0]; // Name column
        const usernameCell = rows[i].getElementsByTagName("td")[5]; // Username column

        if (nameCell && usernameCell) {
          const nameText = nameCell.textContent || nameCell.innerText;
          const usernameText =
            usernameCell.textContent || usernameCell.innerText;

          // Search in both name and username
          if (
            nameText.toLowerCase().indexOf(filter) > -1 ||
            usernameText.toLowerCase().indexOf(filter) > -1
          ) {
            rows[i].style.display = "";
          } else {
            rows[i].style.display = "none";
          }
        }
      }
    });
  });
</script>
{% endblock %}

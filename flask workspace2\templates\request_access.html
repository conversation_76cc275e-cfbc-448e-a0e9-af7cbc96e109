{% extends "base.html" %} {% block auth_content %}

<div class="login-container"><div class="login-card" style="max-width: 600px"><div class="login-header"><div class="login-logo"><i class="fas fa-user-plus"></i></div><h2>Request Access</h2><p>
  Fill out the form below to request access to the GRS Wall Designer
  application.
  </p></div><div class="login-form"><form method="POST" action="{{ url_for('request_access') }}"><div class="form-group"><label for="full_name">Full Name*</label><input
    type="text"
    class="form-control"
    name="full_name"
    required
    placeholder="Enter your full name"
    /></div><div class="form-group"><label for="email">Email Address*</label><input
    type="email"
    class="form-control"
    name="email"
    required
    placeholder="Enter your email address"
    /><small class="form-text text-muted">
    We'll send your credentials to this address if approved.
    </small></div><div class="form-group"><label for="organization">Organization/Institution</label><input
    type="text"
    class="form-control"
    name="organization"
    placeholder="Enter your organization name"
    /></div><div class="form-group"><label for="purpose">Purpose for Access*</label><textarea
    class="form-control"
    name="purpose"
    rows="4"
    required
    placeholder="Please describe how you intend to use this application..."
    ></textarea></div><button type="submit" class="btn btn-primary btn-login"><i class="fas fa-paper-plane"></i>
    Submit Request
  </button></form><div class="login-footer"><p>Already have an account?</p><a href="{{ url_for('login') }}" class="btn btn-outline-primary"><i class="fas fa-arrow-left"></i>
    Back to Login
  </a></div></div></div></div><style>
  /* Override any conflicting body styles for request access page */
  body {
  margin: 0 !important;
  padding: 0 !important;
  background: none !important;
  }

  html {
  margin: 0 !important;
  padding: 0 !important;
  }

  /* Ensure auth container fills entire viewport */
  .auth-container {
  min-height: 100vh !important;
  width: 100vw !important;
  margin: 0 !important;
  padding: 0 !important;
  background: linear-gradient(
  135deg,
  var(--primary-color) 0%,
  var(--primary-dark) 100%
  ) !important;
  overflow-x: hidden !important;
  }

  /* Modern Request Access Page Styles */
  .login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  width: 100%;
  box-sizing: border-box;
  }

  /* Fix text color - make header text white for better contrast on blue background */
  .login-header h2 {
  color: white !important;
  }

  .login-header p {
  color: rgba(255, 255, 255, 0.9) !important;
  }

  /* Form labels - make them white for better contrast on blue background */
  .form-group label {
  color: white !important;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: block;
  }

  /* Form helper text - make it light gray for visibility */
  .form-text,
  .text-muted {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8) !important;
  margin-top: 0.25rem;
  display: block;
  }

  /* Login footer text - make it white */
  .login-footer p {
  color: white !important;
  margin-bottom: 1rem;
  }

  /* Outline button styling for "Back to Login" */
  .btn-outline-primary {
  background-color: transparent !important;
  border: 2px solid white !important;
  color: white !important;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 44px;
  box-sizing: border-box;
  }

  .btn-outline-primary:hover {
  background-color: white !important;
  color: var(--primary-color) !important;
  border-color: white !important;
  text-decoration: none;
  }

  textarea.form-control {
  resize: vertical;
  min-height: 100px;
  }
</style><script>
  document.addEventListener("DOMContentLoaded", function () {
  const form = document.querySelector("form");
  const submitButton = form.querySelector('button[type="submit"]');

  form.addEventListener("submit", function (e) {
  e.preventDefault(); // Prevent default form submission

  // Get form data
  const formData = {
  full_name: form.full_name.value,
  email: form.email.value,
  organization: form.organization.value,
  purpose: form.purpose.value,
  };

  // Disable submit button and show loading state
  const originalText = submitButton.innerHTML;
  submitButton.innerHTML =
  '<i class="fas fa-spinner fa-spin"></i> Submitting...';
  submitButton.disabled = true;

  // Send AJAX request
  fetch("/request_access", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify(formData),
  })
  .then((response) => response.json())
  .then((data) => {
    // Show popup based on response
    if (data.status === "success") {
    alert(
    "Thank you for your request!\n\nWe will contact you soon regarding your access request."
    );
    // Redirect to login page after user clicks OK
    window.location.href = "/login";
    } else if (data.status === "info") {
    alert(data.message);
    // Redirect to login page
    window.location.href = "/login";
    } else {
    alert("Error: " + data.message);
    // Re-enable the submit button
    submitButton.innerHTML = originalText;
    submitButton.disabled = false;
    }
  })
  .catch((error) => {
    console.error("Error:", error);
    alert(
    "An error occurred while submitting your request. Please try again."
    );
    // Re-enable the submit button
    submitButton.innerHTML = originalText;
    submitButton.disabled = false;
  });
  });
  });
</script>

{% endblock %}
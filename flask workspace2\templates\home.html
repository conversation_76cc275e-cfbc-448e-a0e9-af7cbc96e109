{% extends "base.html" %} {% block page_title %}Home - GRS Wall Designer{%
endblock %} {% block content %}
<div class="home-container"><div class="hero"><h1>Welcome to GRS Wall Designer</h1><p class="tagline">
  Pioneering Geosynthetic Reinforced Soil (GRS) Wall Design Solutions as per
  IS 18591:2024
  </p></div><div class="features"><div class="feature"><i class="fas fa-cogs"></i><h2>IS 18591:2024 Compliant Design</h2><p>
  Our software adheres to the latest Indian Standard IS 18591:2024 for
  Geosynthetic Reinforced Soil Structures, ensuring compliance with the
  Load and Resistance Factor Design (LRFD) philosophy.
  </p></div><div class="feature"><i class="fas fa-project-diagram"></i><h2>Interactive Geometry Visualization</h2><p>
  Visualize your GRS wall designs in real-time with our interactive
  modeling tools. Adjust parameters and see instant updates for precise
  design optimization.
  </p></div><div class="feature"><i class="fas fa-file-alt"></i><h2>Professional Report Generation</h2><p>
  Generate comprehensive, concise, and professional reports tailored for
  project submissions. All inputs, and results are presented in a clear
  and structured format.
  </p></div><div class="feature"><i class="fas fa-users"></i><h2>User-Centric Interface</h2><p>
  Designed with engineers in mind, our intuitive interface ensures
  seamless navigation and usability for professionals at all experience
  levels.
  </p></div><div class="feature"><i class="fas fa-globe-asia"></i><h2>Make in India Initiative</h2><p>
  Proudly developed in India, our software supports the nation's vision of
  self-reliance and technological advancement in civil engineering.
  </p></div></div><div class="workflow-section"><div class="cta"><h2>Ready to Revolutionize Your GRS Wall Designs?</h2><p>
  Start your journey with the most advanced and IS 18591:2024 compliant GRS
  wall design software. Select an option from the navigation menu to begin
  your GRS wall design journey.
  </p><button id="start-journey-btn" class="cta-button">Start Your Journey</button></div></div></div><script>
  document.addEventListener("DOMContentLoaded", function () {
  const startJourneyBtn = document.getElementById("start-journey-btn");

  if (startJourneyBtn) {
  startJourneyBtn.addEventListener("click", function () {
          window.location.href = "{{ url_for('project_info') }}";
  });
  }
  });
  </script>

  {% endblock %}
</div>
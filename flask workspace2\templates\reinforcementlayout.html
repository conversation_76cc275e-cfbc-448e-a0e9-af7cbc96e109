{% extends "base.html" %}

{% block content %}
<div id="reinforcementlayout-form">
  <h2>Reinforcement Layout</h2>
  <div class="table-container">
    <table id="reinforcementLayoutTable" class="table table-bordered">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>Location from bottom (m)</th>
          <th>Length (m)</th>
          <th>Coverage Ratio</th>
          <th>Reinforcement Type</th>
          <th>Connection Strength RF (Long-Term)</th>
          <th>Connection Strength RF (Bio-Chem)</th>
          <th>Remove</th>
        </tr>
      </thead>
      <tbody>
        <!-- Rows will be dynamically added here -->
      </tbody>
    </table>
  </div>
  <div class="button-row mt-3">
    <button type="button" id="addReinforcementLayoutRowBtn" class="btn btn-primary me-2">
      Add Row
    </button>
    <button type="button" id="saveReinforcementLayoutBtn" class="btn btn-success">
      Save Layout
    </button>
  </div>
  
  <div class="note alert alert-info mt-4">
    <p class="mb-0">
      <strong>Note:</strong> All numeric values must follow these guidelines:
      <ul>
        <li>Location from bottom must be non-negative</li>
        <li>Length must be greater than 0</li>
        <li>Coverage ratio must be between 0 and 1</li>
        <li>Connection strength reduction factors must be at least 1</li>
      </ul>
    </p>
  </div>
</div>

<!-- GRS Wall Visualization Section -->
<section class="visualization-section">
  <h2>GRS Wall Visualization</h2>
  <div class="visualization-container">
    <div class="canvas-wrapper">
      <canvas id="geometry2-canvas" width="800" height="500"></canvas>
    </div>
    <div class="button-group">
      <button id="zoom-in-button" class="btn btn-primary">Zoom In</button>
      <button id="zoom-out-button" class="btn btn-primary">Zoom Out</button>
      <button id="fit-button" class="btn btn-primary">Fit to Window</button>
      <button id="screenshot-button" class="btn btn-primary">Take Screenshot</button>
    </div>
  </div>
</section>

<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script>
  function initializeReinforcementLayout() {
      console.log("=== INITIALIZING REINFORCEMENT LAYOUT ===");

      // Check if already initialized to prevent duplicate handlers
      if (window.reinforcementLayoutInitialized) {
        console.log("Already initialized, skipping...");
        return;
      }

      console.log("First time initialization, setting up handlers...");

      // Clear any existing event handlers to prevent duplicates
      $("#addReinforcementLayoutRowBtn").off("click");
      $("#saveReinforcementLayoutBtn").off("click");
      $("#reinforcementLayoutTable tbody").off("click", ".btn-remove-layout-row");
      $("#reinforcementLayoutTable tbody").off("change", "input, select");
      $("#reinforcementLayoutTable").off("input", "input, select");
      $("#reinforcementLayoutTable tbody input").off('input change blur');

      let rowCounter = 0; // Initialize at the top

      // Validation rules for different fields
      const validationRules = {
          location: {
              min: 0,
              message: "Location must be non-negative"
          },
          length: {
              min: 0.1,
              message: "Length must be greater than 0"
          },
          coverage_ratio: {
              min: 0,
              max: 1,
              message: "Coverage ratio must be between 0 and 1"
          },
          connection_strength_longterm: {
              min: 1,
              message: "Long-term connection strength RF must be at least 1"
          },
          connection_strength_biochem: {
              min: 1,
              message: "Bio-Chem connection strength RF must be at least 1"
          }
      };

      // Function to validate a field
      function validateField(input) {
          const value = parseFloat(input.value);
          const name = input.name;
          const fieldType = name.substring(0, name.lastIndexOf('_'));
          const rule = validationRules[fieldType];
          
          // Reset previous error state
          $(input).removeClass('is-invalid');
          $(input).next('.invalid-feedback').remove();
          
          // Skip validation for empty fields (they're handled by the required attribute)
          if (input.value.trim() === '') {
              return false;
          }
          
          // Apply validation rules if they exist for this field type
          if (rule) {
              if (isNaN(value)) {
                  $(input).addClass('is-invalid');
                  $('<div class="invalid-feedback">Please enter a valid number</div>').insertAfter(input);
                  return false;
              }
              
              if (rule.min !== undefined && value < rule.min) {
                  $(input).addClass('is-invalid');
                  $('<div class="invalid-feedback">' + rule.message + '</div>').insertAfter(input);
                  return false;
              }
              
              if (rule.max !== undefined && value > rule.max) {
                  $(input).addClass('is-invalid');
                  $('<div class="invalid-feedback">' + rule.message + '</div>').insertAfter(input);
                  return false;
              }
          }
          
          return true;
      }

      // Function to add a new row to the reinforcement layout table
      function addReinforcementLayoutRow() {
          rowCounter++; // Increment rowCounter to ensure unique IDs

          // Get reinforcement types from localStorage (from properties page)
          let reinforcementOptions = '<option value="">Select Reinforcement Type</option>';
          const storedReinforcementData = localStorage.getItem("reinforcementData");
          if (storedReinforcementData) {
              const reinforcementData = JSON.parse(storedReinforcementData);
              reinforcementData.forEach(item => {
                  if (item.name && item.name.trim() !== '') {
                      reinforcementOptions += `<option value="${item.name}">${item.name}</option>`;
                  }
              });
          }

          // Fallback to server data if localStorage is empty
          if (reinforcementOptions === '<option value="">Select Reinforcement Type</option>') {
              {% for item in reinforcement_data %}
                  reinforcementOptions += `<option value="{{ item['name'] }}">{{ item['name'] }}</option>`;
              {% endfor %}
          }

          const newRow = `
              <tr id="row_${rowCounter}">
                  <td>${rowCounter}</td>
                  <td>
                      <input type="number" name="location_${rowCounter}" step="0.1" min="0" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td>
                      <input type="number" name="length_${rowCounter}" step="0.1" min="0.1" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td>
                      <input type="number" name="coverage_ratio_${rowCounter}" step="0.01" min="0" max="1" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td>
                      <select name="reinforcement_type_${rowCounter}" required class="form-control">
                          ${reinforcementOptions}
                      </select>
                  </td>
                  <td>
                      <input type="number" name="connection_strength_longterm_${rowCounter}" step="0.01" min="1" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td>
                      <input type="number" name="connection_strength_biochem_${rowCounter}" step="0.01" min="1" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td><button type="button" class="btn btn-danger btn-remove-layout-row" data-row="${rowCounter}">Remove</button></td>
              </tr>
          `;
          $("#reinforcementLayoutTable tbody").append(newRow);

          // Event delegation will automatically handle the new inputs
          // No need to manually attach handlers since we're using delegation

          // Save and update visualization
          saveLayoutToLocalStorage();

          // Trigger immediate visualization update for the new row
          setTimeout(() => {
              updateVisualization();
          }, 50);
      }

      // Function to add a new row with data
      function addReinforcementLayoutRowWithData(rowData) {
          rowCounter++;

          // Get reinforcement types from localStorage (from properties page)
          let reinforcementOptions = '<option value="">Select Reinforcement Type</option>';
          const storedReinforcementData = localStorage.getItem("reinforcementData");
          if (storedReinforcementData) {
              const reinforcementData = JSON.parse(storedReinforcementData);
              reinforcementData.forEach(item => {
                  if (item.name && item.name.trim() !== '') {
                      const isSelected = rowData.reinforcement_type === item.name ? 'selected' : '';
                      reinforcementOptions += `<option value="${item.name}" ${isSelected}>${item.name}</option>`;
                  }
              });
          }

          // Fallback to server data if localStorage is empty
          if (reinforcementOptions === '<option value="">Select Reinforcement Type</option>') {
              {% for item in reinforcement_data %}
                  reinforcementOptions += `<option value="{{ item['name'] }}" ${rowData.reinforcement_type === "{{ item['name'] }}" ? 'selected' : ''}>{{ item['name'] }}</option>`;
              {% endfor %}
          }

          const newRow = `
              <tr id="row_${rowCounter}">
                  <td>${rowCounter}</td>
                  <td>
                      <input type="number" name="location_${rowCounter}" value="${rowData.location}" step="0.1" min="0" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td>
                      <input type="number" name="length_${rowCounter}" value="${rowData.length}" step="0.1" min="0.1" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td>
                      <input type="number" name="coverage_ratio_${rowCounter}" value="${rowData.coverage_ratio}" step="0.01" min="0" max="1" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td>
                      <select name="reinforcement_type_${rowCounter}" required class="form-control">
                          ${reinforcementOptions}
                      </select>
                  </td>
                  <td>
                      <input type="number" name="connection_strength_longterm_${rowCounter}" value="${rowData.connection_strength_longterm}" step="0.01" min="1" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td>
                      <input type="number" name="connection_strength_biochem_${rowCounter}" value="${rowData.connection_strength_biochem}" step="0.01" min="1" required class="form-control">
                      <div class="invalid-feedback"></div>
                  </td>
                  <td><button type="button" class="btn btn-danger btn-remove-layout-row" data-row="${rowCounter}">Remove</button></td>
              </tr>
          `;
          $("#reinforcementLayoutTable tbody").append(newRow);

          // Event delegation will automatically handle the loaded inputs
          // No need to manually attach handlers since we're using delegation
      }

      // Function to remove a row
      function removeReinforcementLayoutRow(rowId) {
          const rowCount = $("#reinforcementLayoutTable tbody tr").length;
          if (rowCount <= 1) {
              if (typeof showErrorPopup === "function") {
                  showErrorPopup("Cannot remove the last row. Please keep at least one row.");
              } else {
                  alert("Cannot remove the last row. Please keep at least one row.");
              }
              return;
          }

          $("#row_" + rowId).remove();
          saveLayoutToLocalStorage();
      }

      // Function to save layout to localStorage
      function saveLayoutToLocalStorage() {
          const layoutData = [];

          $("#reinforcementLayoutTable tbody tr").each(function() {
              const row = $(this);
              const rowData = {
                  location: row.find("input[name^='location']").val(),
                  length: row.find("input[name^='length']").val(),
                  coverage_ratio: row.find("input[name^='coverage_ratio']").val(),
                  reinforcement_type: row.find("select[name^='reinforcement_type']").val(),
                  connection_strength_longterm: row.find("input[name^='connection_strength_longterm']").val(),
                  connection_strength_biochem: row.find("input[name^='connection_strength_biochem']").val()
              };
              layoutData.push(rowData);
          });

          localStorage.setItem('reinforcementLayoutData', JSON.stringify(layoutData));
          console.log("💾 Saved layout data to localStorage:", layoutData);

          // Note: updateVisualization() is called by the input handlers, not here
          // to avoid duplicate calls
      }

      // Function to load layout from localStorage
      function loadLayoutFromLocalStorage() {
          const storedLayoutData = localStorage.getItem('reinforcementLayoutData');

          if (storedLayoutData) {
              const layoutData = JSON.parse(storedLayoutData);

              // Clear existing rows
              $("#reinforcementLayoutTable tbody").empty();

              // Reset rowCounter based on loaded data
              rowCounter = 0;

              // Rebuild the table with data from localStorage
              layoutData.forEach(rowData => {
                  addReinforcementLayoutRowWithData(rowData);
              });
          } else {
              // If there's no data in local storage, add an initial row
              addReinforcementLayoutRow();
          }
      }

      // Enhanced updateVisualization function for real-time updates
      function updateVisualization() {
          console.log("🎨 updateVisualization called - collecting current form data");

          // Get current layout data from the form
          const layoutData = [];
          $("#reinforcementLayoutTable tbody tr").each(function() {
              const row = $(this);
              const rowData = {
                  location: parseFloat(row.find("input[name^='location']").val()) || 0,
                  length: parseFloat(row.find("input[name^='length']").val()) || 0,
                  coverage_ratio: parseFloat(row.find("input[name^='coverage_ratio']").val()) || 0,
                  reinforcement_type: row.find("select[name^='reinforcement_type']").val() || '',
                  connection_strength_longterm: parseFloat(row.find("input[name^='connection_strength_longterm']").val()) || 0,
                  connection_strength_biochem: parseFloat(row.find("input[name^='connection_strength_biochem']").val()) || 0
              };
              // Include all rows, even empty ones, to show the structure
              layoutData.push(rowData);
          });

          console.log("🎨 Current layout data:", layoutData);

          // Update localStorage with the latest data
          localStorage.setItem('reinforcementLayoutData', JSON.stringify(layoutData));

          // Call the redrawCanvas function with fresh data
          if (window.redrawCanvas) {
              console.log("🎨 Calling window.redrawCanvas for real-time update");
              window.redrawCanvas(layoutData);
          } else {
              console.warn("🎨 window.redrawCanvas not available, trying alternatives");

              // Try alternative approach
              if (window.updateReinforcementLayoutFromForm) {
                  console.log("🎨 Trying updateReinforcementLayoutFromForm");
                  window.updateReinforcementLayoutFromForm();
              } else {
                  console.warn("🎨 No visualization update functions available");

                  // Last resort: try to trigger initialization again
                  if (typeof initializeLayoutVisualization === 'function') {
                      console.log("🎨 Re-initializing layout visualization");
                      setTimeout(() => {
                          initializeLayoutVisualization();
                      }, 100);
                  }
              }
          }
      }

      // Load layout from localStorage on page load
      loadLayoutFromLocalStorage();

      // Trigger initial visualization update after loading data
      setTimeout(() => {
          console.log("🎨 Triggering initial visualization after data load");
          updateVisualization();
      }, 100);

      // Initialize the layout visualization with proper timing
      function initializeVisualizationWithRetry() {
          // Check if required geometry data exists
          const geometryData = localStorage.getItem('geometryData');
          console.log("🎨 Checking geometry data:", geometryData);

          if (!geometryData) {
              console.warn("🎨 No geometry data found - will use placeholder values for visualization only");
              // DO NOT set localStorage - just let the visualization use placeholder values
          }

          // Try to initialize the visualization
          if (typeof initializeLayoutVisualization === 'function') {
              console.log("🎨 Found initializeLayoutVisualization, calling it...");
              try {
                  initializeLayoutVisualization();

                  // Trigger initial visualization update
                  setTimeout(() => {
                      console.log("🎨 Triggering initial visualization update");
                      updateVisualization();
                  }, 300);

                  return true; // Success
              } catch (error) {
                  console.error("🎨 Error initializing layout visualization:", error);
                  return false;
              }
          } else {
              console.warn("🎨 initializeLayoutVisualization function not found, will retry...");
              return false;
          }
      }

      // Try initialization with retries
      let retryCount = 0;
      const maxRetries = 15;

      function tryInitialization() {
          console.log(`🎨 Attempt ${retryCount + 1}/${maxRetries} - checking for initializeLayoutVisualization...`);
          console.log(`🎨 Function available: ${typeof window.initializeLayoutVisualization}`);

          if (initializeVisualizationWithRetry()) {
              console.log("🎨 Visualization initialized successfully!");
              return;
          }

          retryCount++;
          if (retryCount < maxRetries) {
              console.log(`🎨 Retry ${retryCount}/${maxRetries} in 300ms...`);
              setTimeout(tryInitialization, 300);
          } else {
              console.error("🎨 Failed to initialize visualization after", maxRetries, "attempts");
              console.log("🎨 Available window functions:", Object.keys(window).filter(key => key.includes('Layout') || key.includes('Visualization')));

              // Final fallback: draw basic canvas
              const canvas = document.getElementById('geometry2-canvas');
              if (canvas) {
                  console.log("🎨 Drawing fallback canvas");
                  const ctx = canvas.getContext('2d');
                  if (ctx) {
                      // Set canvas size
                      canvas.width = 800;
                      canvas.height = 500;

                      // Clear canvas
                      ctx.clearRect(0, 0, canvas.width, canvas.height);

                      // Draw basic wall structure
                      ctx.fillStyle = '#f0f0f0';
                      ctx.fillRect(0, 0, canvas.width, canvas.height);

                      // Draw a simple wall
                      ctx.fillStyle = '#D6B85A';
                      ctx.fillRect(100, 200, 300, 200);

                      ctx.fillStyle = '#666';
                      ctx.fillRect(90, 200, 20, 200);

                      ctx.fillStyle = '#333';
                      ctx.font = '16px Arial';
                      ctx.fillText('Basic Wall Structure (Fallback)', 150, 100);
                      ctx.fillText('Visualization script not loaded properly', 120, 120);
                      ctx.fillText('Please refresh the page', 120, 140);
                  }
              }
          }
      }

      // Start initialization attempts after a short delay to ensure DOM is ready
      setTimeout(tryInitialization, 200);

      // Enhanced event handler for real-time updates (like geometry and external loads)
      function handleReinforcementInputChange() {
          console.log("🔥 REINFORCEMENT INPUT CHANGED:", this.name, "value:", this.value);

          // Validate the field
          validateField(this);

          // Save to localStorage
          saveLayoutToLocalStorage();

          // Immediate visualization update (like geometry section)
          console.log("🔥 About to call updateVisualization...");
          setTimeout(() => {
              console.log("🔥 Calling updateVisualization now!");
              updateVisualization();
          }, 10);
      }

      // Use event delegation for dynamic content (like geometry section)
      function setupReinforcementListeners() {
          console.log("Setting up event delegation for reinforcement layout...");

          // Remove any existing delegated listeners to prevent duplicates
          $(document).off('input.reinforcement change.reinforcement');

          // Add delegated event listeners
          $(document).on('input.reinforcement change.reinforcement', '#reinforcementLayoutTable input, #reinforcementLayoutTable select', handleReinforcementInputChange);

          console.log("Event delegation setup complete for reinforcement inputs");
      }

      // Setup the enhanced event listeners
      setupReinforcementListeners();

      // Event handler for the "Add Row" button
      $("#addReinforcementLayoutRowBtn").on("click", function() {
          console.log("Add Row button clicked!");
          addReinforcementLayoutRow();
      });

      // Event handler for removing a row
      $("#reinforcementLayoutTable tbody").on('click', '.btn-remove-layout-row', function() {
          const rowId = $(this).data('row');
          removeReinforcementLayoutRow(rowId);
      });

      // Event handler for saving the layout (to server)
      $("#saveReinforcementLayoutBtn").on("click", function() {
          console.log("Save Layout button clicked!");
          // Validate all fields first
          let isValid = true;
          $("#reinforcementLayoutTable tbody input").each(function() {
              if (!validateField(this)) {
                  isValid = false;
              }
          });
          
          if (!isValid) {
              if (typeof showErrorPopup === "function") {
                showErrorPopup("Please fix the validation errors before saving the layout.");
              } else {
                alert("Please fix the validation errors before saving the layout.");
              }
              return;
          }
          
          const layoutData = [];

          $("#reinforcementLayoutTable tbody tr").each(function() {
              const row = $(this);
              const rowData = {
                  location: row.find("input[name^='location']").val(),
                  length: row.find("input[name^='length']").val(),
                  coverage_ratio: row.find("input[name^='coverage_ratio']").val(),
                  reinforcement_type: row.find("select[name^='reinforcement_type']").val(),
                  connection_strength_longterm: row.find("input[name^='connection_strength_longterm']").val(),
                  connection_strength_biochem: row.find("input[name^='connection_strength_biochem']").val()
              };
              layoutData.push(rowData);
          });

          // Send the layoutData to the server using AJAX
          $.ajax({
              type: "POST",
              url: "/reinforcementlayout",
              contentType: "application/json",
              data: JSON.stringify(layoutData),
              success: function(response) {
                  if (typeof showSuccessPopup === "function") {
                    showSuccessPopup("Reinforcement layout saved successfully!");
                  } else {
                    alert("Reinforcement layout saved successfully!");
                  }

                  // Update sidebar status indicator
                  if (typeof updateSidebarStatus === "function") {
                    updateSidebarStatus("reinforcementlayout-form");
                  }

                  // Don't reload the page - let AJAX navigation handle it
                  // window.location.href = window.location.pathname;
              },
              error: function(error) {
                  if (typeof showErrorPopup === "function") {
                    showErrorPopup("Error saving reinforcement layout.");
                  } else {
                    alert("Error saving reinforcement layout.");
                  }
              }
          });
      });

      // Note: Visualization updates are now handled in the main input change handler above
      // to avoid duplicate event listeners
      
      // Add validation and visualization updates to existing inputs on page load
      $("#reinforcementLayoutTable tbody input, #reinforcementLayoutTable tbody select").each(function() {
          $(this).on('input change blur', function() {
              validateField(this);
              // Update visualization for existing inputs too
              setTimeout(() => {
                  updateVisualization();
              }, 10);
          });
      });

      // Mark as initialized to prevent duplicate handlers
      window.reinforcementLayoutInitialized = true;
      console.log("=== REINFORCEMENT LAYOUT INITIALIZATION COMPLETE ===");
  }

  // Make functions available globally IMMEDIATELY (before DOM ready)
  window.initializeReinforcementLayout = initializeReinforcementLayout;

  // Initialize on DOM ready
  $(document).ready(function() {
      console.log("DOM ready - calling initializeReinforcementLayout");
      initializeReinforcementLayout();
  });

  // Also initialize immediately if DOM is already ready (for AJAX navigation)
  if (document.readyState === 'loading') {
    console.log("Document still loading, waiting for DOM ready");
  } else {
    console.log("Document already ready, initializing immediately");
    // DOM is already ready, initialize immediately
    setTimeout(function() {
      initializeReinforcementLayout();
    }, 100);
  }
</script>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Check if we're on the reinforcement layout page
    const layoutTable = document.getElementById("reinforcementLayoutTable");
    if (!layoutTable) {
      console.warn("Reinforcement layout elements not found, skipping initialization");
      return;
    }

    const geometryData = JSON.parse(localStorage.getItem("geometryData"));
    const externalloads_data =
      JSON.parse(localStorage.getItem("externalloads_data")) || {};

    // Setup the impact_loads with the correct structure
    if (
      externalloads_data.impact_loads &&
      Array.isArray(externalloads_data.impact_loads)
    ) {
      // Convert from array to object if needed
      externalloads_data.impact_loads = {
        rupture: {
          upper: externalloads_data.impact_loads[0] || 0,
          second: externalloads_data.impact_loads[1] || 0,
        },
        pullout: {
          upper: externalloads_data.impact_loads[2] || 0,
          second: externalloads_data.impact_loads[3] || 0,
        },
      };
    }
  });
</script>

<style>
  .button-row {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    margin-bottom: 30px;
  }

  .table-container {
    margin-bottom: 15px;
    overflow-x: auto;
  }

  /* Button styling now handled by Bootstrap classes */

  .visualization-section {
    background: linear-gradient(145deg, #fdfaf6, #efe7dc);
    border-radius: 16px;
    box-shadow: 8px 8px 20px rgba(0, 0, 0, 0.05),
      -8px -8px 20px rgba(255, 255, 255, 0.8);
    padding: 30px;
    margin: auto;
    width: 90%;
    max-width: 900px;
    border: 1px solid #d6c4b1;
  }

  .visualization-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .canvas-wrapper {
    background: linear-gradient(145deg, #f5e6d7, #e6d3b8);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #c4a785;
    width: 100%;
    max-width: 800px;
    overflow: hidden;
    box-sizing: border-box;
    box-shadow: inset 6px 6px 12px rgba(0, 0, 0, 0.05),
      inset -6px -6px 12px rgba(255, 255, 255, 0.8);
  }

  canvas {
    display: block;
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    background-color: #fff;
    border: 1px solid #c4a785;
  }

  .button-group {
    margin-top: 20px;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
  }

  /* Button group styling removed to match external loads section */
  /* Buttons now inherit from global styles for consistency */

  .form-control.is-invalid {
    border-color: #dc3545;
    background-color: #fff8f8;
  }

  .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #dc3545;
  }

  .note {
    background-color: #f8f9fa;
    border-left: 4px solid #a47551;
    padding: 15px;
    margin-top: 20px;
  }

  @media (max-width: 768px) {
    .button-row {
      flex-direction: column;
      gap: 10px;
    }

    .button-row button {
      width: 100%;
      margin-bottom: 10px;
    }
  }

  @media (max-width: 600px) {
    #screenshot-button {
      width: 100%;
      padding: 12px 16px;
    }
  }
</style>

<script src="{{ url_for('static', filename='js/LayoutVisualization.js') }}"></script>

{% endblock %}

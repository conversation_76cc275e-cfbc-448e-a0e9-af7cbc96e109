{% extends "base.html" %}

{% block content %}
<h1>Reinforcement Properties</h1><form id="reinforcementproperties-form" method="POST" action="javascript:void(0);" onsubmit="return false;"><input type="hidden" id="row_count" name="row_count" value="{{ reinforcement_data|length }}"/><div class="table-responsive"><table id="reinforcementTable" class="table table-bordered table-striped"><thead class="table-light"><tr><th>Type ID</th><th>Name</th><th>Tult (kN/m)</th><th>RF ID</th><th>RF W</th><th>RF CR</th><th>FS</th><th>Pullout Angle</th><th>Sliding Angle</th><th>Scale Factor</th><th>Remove</th></tr></thead><tbody>
        {% for item in reinforcement_data %}
  <tr><td><input type="text" name="type_id_{{ loop.index0 }}" value="{{ item.get('type_id', '') }}" required /><span class="error-message" id="type_id_{{ loop.index0 }}-error"></span></td><td><input type="text" name="name_{{ loop.index0 }}" value="{{ item.get('name', '') }}" required /><span class="error-message" id="name_{{ loop.index0 }}-error"></span></td><td><input type="number" step="0.01" min="0" name="tult_{{ loop.index0 }}" value="{{ item.get('tult', '') }}" required /><span class="error-message" id="tult_{{ loop.index0 }}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfid_{{ loop.index0 }}" value="{{ item.get('rfid', '') }}" required /><span class="error-message" id="rfid_{{ loop.index0 }}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfw_{{ loop.index0 }}" value="{{ item.get('rfw', '') }}" required /><span class="error-message" id="rfw_{{ loop.index0 }}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfcr_{{ loop.index0 }}" value="{{ item.get('rfcr', '') }}" required /><span class="error-message" id="rfcr_{{ loop.index0 }}-error"></span></td><td><input type="number" step="0.01" min="1" name="fs_{{ loop.index0 }}" value="{{ item.get('fs', '') }}" required /><span class="error-message" id="fs_{{ loop.index0 }}-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="pullout_angle_{{ loop.index0 }}" value="{{ item.get('pullout_angle', '') }}" required /><span class="error-message" id="pullout_angle_{{ loop.index0 }}-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="sliding_angle_{{ loop.index0 }}" value="{{ item.get('sliding_angle', '') }}" required /><span class="error-message" id="sliding_angle_{{ loop.index0 }}-error"></span></td><td><input type="number" step="0.01" min="0" name="scale_factor_{{ loop.index0 }}" value="{{ item.get('scale_factor', '') }}" required /><span class="error-message" id="scale_factor_{{ loop.index0 }}-error"></span></td><td><button type="button" class="btn btn-danger btn-sm remove-row">Remove</button></td></tr>
        {% else %}
  <tr><td><input type="text" name="type_id_0" value="" required /><span class="error-message" id="type_id_0-error"></span></td><td><input type="text" name="name_0" value="" required /><span class="error-message" id="name_0-error"></span></td><td><input type="number" step="0.01" min="0" name="tult_0" value="" required /><span class="error-message" id="tult_0-error"></span></td><td><input type="number" step="0.01" min="1" name="rfid_0" value="" required /><span class="error-message" id="rfid_0-error"></span></td><td><input type="number" step="0.01" min="1" name="rfw_0" value="" required /><span class="error-message" id="rfw_0-error"></span></td><td><input type="number" step="0.01" min="1" name="rfcr_0" value="" required /><span class="error-message" id="rfcr_0-error"></span></td><td><input type="number" step="0.01" min="1" name="fs_0" value="" required /><span class="error-message" id="fs_0-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="pullout_angle_0" value="" required /><span class="error-message" id="pullout_angle_0-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="sliding_angle_0" value="" required /><span class="error-message" id="sliding_angle_0-error"></span></td><td><input type="number" step="0.01" min="0" name="scale_factor_0" value="" required /><span class="error-message" id="scale_factor_0-error"></span></td><td><button type="button" class="btn btn-danger btn-sm remove-row">Remove</button></td></tr>
        {% endfor %}
  </tbody></table></div><div class="button-row mt-3"><button type="button" id="add-row" class="btn btn-primary me-2">Add Row</button><button type="submit" class="btn btn-success">Save Reinforcement Properties</button></div></form><!-- Note Section --><div class="note alert alert-info mt-4"><p class="mb-0"><strong>Note:</strong> Ensure all reinforcement properties are accurately entered for proper analysis.
  <ul><li>Tult (kN/m): Ultimate tensile strength must be greater than 0</li><li>RF values (ID, W, CR): Reduction factors must be at least 1</li><li>FS: Factor of safety must be at least 1</li><li>Pullout/Sliding Angles: Must be between 0 and 90 degrees</li><li>Scale Factor: Must be a non-negative value</li></ul></p></div><script src="https://code.jquery.com/jquery-3.6.4.min.js"></script><script>
  function initializeReinforcementProperties() {
  console.log("=== INITIALIZING REINFORCEMENT PROPERTIES ===");

  // Check if jQuery is available
  if (typeof $ === 'undefined') {
  console.warn("jQuery not available, deferring reinforcement properties initialization");
  setTimeout(initializeReinforcementProperties, 100);
  return;
  }

  // Check if required elements exist
  const form = $("#reinforcementproperties-form");
  const table = $("#reinforcementTable");
  const addRowButton = $("#add-row");

  if (!form.length || !table.length || !addRowButton.length) {
  console.warn("Reinforcement properties elements not found, skipping initialization");
  return;
  }

  // Check if already initialized to prevent duplicate handlers
  if (window.reinforcementPropertiesInitialized) {
  console.log("Already initialized, skipping...");
  return;
  }

  // Additional check: if add-row button already has our specific event handler, skip
  const addRowButtonEl = addRowButton[0];
  if (addRowButtonEl) {
  const events = $._data(addRowButtonEl, "events");
  if (events && events.click && events.click.some(e => e.namespace === "addRow")) {
  console.log("Add row button already has event handler, skipping initialization...");
  window.reinforcementPropertiesInitialized = true;
  return;
  }
  }

  console.log("First time initialization, setting up handlers...");
  // Validation rules for each field type
  const validationRules = {
  type_id: {
  test: value => value.trim().length > 0,
  message: "Type ID is required"
  },
  name: {
  test: value => value.trim().length > 0,
  message: "Name is required"
  },
  tult: {
  test: value => parseFloat(value) > 0,
  message: "Tensile strength must be greater than 0"
  },
  rfid: {
  test: value => parseFloat(value) >= 1,
  message: "RF ID must be at least 1"
  },
  rfw: {
  test: value => parseFloat(value) >= 1,
  message: "RF W must be at least 1"
  },
  rfcr: {
  test: value => parseFloat(value) >= 1,
  message: "RF CR must be at least 1"
  },
  fs: {
  test: value => parseFloat(value) >= 1,
  message: "Factor of safety must be at least 1"
  },
  pullout_angle: {
  test: value => parseFloat(value) >= 0 && parseFloat(value) <= 90,
  message: "Pullout angle must be between 0 and 90 degrees"
  },
  sliding_angle: {
  test: value => parseFloat(value) >= 0 && parseFloat(value) <= 90,
  message: "Sliding angle must be between 0 and 90 degrees"
  },
  scale_factor: {
  test: value => parseFloat(value) >= 0,
  message: "Scale factor must be non-negative"
  }
  };

  // Function to validate a field
  function validateField(input) {
  const name = $(input).attr('name');
  const baseFieldName = name.substring(0, name.lastIndexOf('_'));
  const index = name.substring(name.lastIndexOf('_') + 1);
  const value = $(input).val();
  const errorElement = $(`#${name.replace(/\./g, '\\.')}-error`);

  // Get validation rule for this field type
  const rule = validationRules[baseFieldName];
  if (!rule) return true;

  // Check if value is empty first
  if (!value.trim()) {
  errorElement.text("This field is required");
  $(input).addClass('invalid');
  return false;
  }

  // For number fields, validate the value
  if ($(input).attr('type') === 'number') {
  if (isNaN(parseFloat(value))) {
    errorElement.text("Please enter a valid number");
    $(input).addClass('invalid');
    return false;
  }

  // Check against specific rule
  if (!rule.test(value)) {
    errorElement.text(rule.message);
    $(input).addClass('invalid');
    return false;
  }
  } else {
  // For text fields
  if (!rule.test(value)) {
    errorElement.text(rule.message);
    $(input).addClass('invalid');
    return false;
  }
  }

  // If validation passes
  errorElement.text("");
  $(input).removeClass('invalid');
  return true;
  }

  // Function to add a new row to the table - with duplicate prevention
  $("#add-row").off("click.addRow").on("click.addRow", function(e) {
  e.preventDefault();
  e.stopImmediatePropagation();

  console.log("Add row button clicked - single handler");
  var table = $("#reinforcementTable tbody");
  var newRowIndex = table.children().length;
  var newRow = `
  <tr><td><input type="text" name="type_id_${newRowIndex}" value="" required/><span class="error-message" id="type_id_${newRowIndex}-error"></span></td><td><input type="text" name="name_${newRowIndex}" value="" required/><span class="error-message" id="name_${newRowIndex}-error"></span></td><td><input type="number" step="0.01" min="0" name="tult_${newRowIndex}" value="" required/><span class="error-message" id="tult_${newRowIndex}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfid_${newRowIndex}" value="" required/><span class="error-message" id="rfid_${newRowIndex}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfw_${newRowIndex}" value="" required/><span class="error-message" id="rfw_${newRowIndex}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfcr_${newRowIndex}" value="" required/><span class="error-message" id="rfcr_${newRowIndex}-error"></span></td><td><input type="number" step="0.01" min="1" name="fs_${newRowIndex}" value="" required/><span class="error-message" id="fs_${newRowIndex}-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="pullout_angle_${newRowIndex}" value="" required/><span class="error-message" id="pullout_angle_${newRowIndex}-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="sliding_angle_${newRowIndex}" value="" required/><span class="error-message" id="sliding_angle_${newRowIndex}-error"></span></td><td><input type="number" step="0.01" min="0" name="scale_factor_${newRowIndex}" value="" required/><span class="error-message" id="scale_factor_${newRowIndex}-error"></span></td><td><button type="button" class="btn btn-danger btn-sm remove-row">Remove</button></td></tr>
  `;
  table.append(newRow);

  // Add validation to the new inputs
  table.find('tr:last-child input').each(function() {
  $(this).off('input change blur').on('input change blur', function() {
    validateField(this);
    saveToLocalStorage();
  });
  });

  // Update the row_count hidden field
  updateRowCount();

  // Save to localStorage
  saveToLocalStorage();
  });

  // Function to remove a row from the table
  $(document).on("click", ".remove-row", function() {
  var rowCount = $("#reinforcementTable tbody tr").length;
  if (rowCount <= 1) {
  if (typeof showErrorPopup === "function") {
    showErrorPopup("Cannot remove the last row. Please keep at least one row.");
  } else {
    alert("Cannot remove the last row. Please keep at least one row.");
  }
  return;
  }

  $(this).closest("tr").remove();

  // Update row numbers and row_count hidden field
  $("#reinforcementTable tbody tr").each(function(index) {
  $(this).find("input").each(function() {
    var name = $(this).attr("name");
    if (name) {
    const newName = name.replace(/_\d+$/, "_" + index);
    $(this).attr("name", newName);

    // Update the corresponding error message ID
    const errorSpan = $(this).next(".error-message");
    if (errorSpan.length) {
    errorSpan.attr("id", newName + "-error");
    }
    }
  });
  });

  // Update the row_count hidden field
  updateRowCount();

  // Save changes to localStorage
  saveToLocalStorage();
  });

  // Validate all inputs on change
  $(document).on("input change blur", "#reinforcementTable input", function() {
  validateField(this);
  saveToLocalStorage();
  });

  // Function to save data to localStorage
  function saveToLocalStorage() {
  const reinforcementData = [];

  $("#reinforcementTable tbody tr").each(function(index) {
  const row = $(this);
  const rowData = {
    type_id: row.find('input[name^="type_id_"]').val() || '',
    name: row.find('input[name^="name_"]').val() || '',
    tult: parseFloat(row.find('input[name^="tult_"]').val()) || 0,
    rfid: parseFloat(row.find('input[name^="rfid_"]').val()) || 0,
    rfw: parseFloat(row.find('input[name^="rfw_"]').val()) || 0,
    rfcr: parseFloat(row.find('input[name^="rfcr_"]').val()) || 0,
    fs: parseFloat(row.find('input[name^="fs_"]').val()) || 0,
    pullout_angle: parseFloat(row.find('input[name^="pullout_angle_"]').val()) || 0,
    sliding_angle: parseFloat(row.find('input[name^="sliding_angle_"]').val()) || 0,
    scale_factor: parseFloat(row.find('input[name^="scale_factor_"]').val()) || 0,
  };

  // Only save rows that have at least some meaningful data
  if (rowData.type_id || rowData.name || rowData.tult > 0) {
    reinforcementData.push(rowData);
  }
  });

  console.log("Saving to localStorage:", reinforcementData);
  localStorage.setItem("reinforcementData", JSON.stringify(reinforcementData));
  console.log("Data saved to localStorage successfully");
  }

  // Function to load data from localStorage
  function loadFromLocalStorage() {
  console.log("loadFromLocalStorage() called");
  const storedData = localStorage.getItem("reinforcementData");
  console.log("Stored data:", storedData);

  if (storedData) {
  try {
    const reinforcementData = JSON.parse(storedData);
    console.log("Loading reinforcement data from localStorage:", reinforcementData);

    // Validate that the data is an array
    if (!Array.isArray(reinforcementData)) {
    console.warn("localStorage data is not an array, clearing corrupted data:", reinforcementData);
    localStorage.removeItem("reinforcementData");
    return;
    }

  // Check if we should load from localStorage
  let shouldLoadFromStorage = false;

  // Case 1: No rows at all
  if ($("#reinforcementTable tbody tr").length === 0) {
    shouldLoadFromStorage = true;
    console.log("No rows found, loading from localStorage");
  }
  // Case 2: Only one row and it's empty (typical server fallback)
  else if ($("#reinforcementTable tbody tr").length === 1) {
    const firstRow = $("#reinforcementTable tbody tr:first");
    const typeId = firstRow.find("input[name='type_id_0']").val();
    const name = firstRow.find("input[name='name_0']").val();
    const tult = firstRow.find("input[name='tult_0']").val();

    // If all key fields are empty, it's a server fallback row
    if (!typeId && !name && !tult) {
    shouldLoadFromStorage = true;
    console.log("Found empty fallback row, loading from localStorage");
    }
  }
  // Case 3: Multiple rows but all empty (shouldn't happen but just in case)
  else {
    let allEmpty = true;
    $("#reinforcementTable tbody tr").each(function() {
    const row = $(this);
    const typeId = row.find("input[name^='type_id_']").val();
    const name = row.find("input[name^='name_']").val();
    if (typeId || name) {
    allEmpty = false;
    return false; // break
    }
    });
    if (allEmpty) {
    shouldLoadFromStorage = true;
    console.log("All rows are empty, loading from localStorage");
    }
  }

  if (shouldLoadFromStorage && reinforcementData.length > 0) {
    $("#reinforcementTable tbody").empty();
    console.log("Table cleared, adding rows from localStorage...");

    reinforcementData.forEach((item, index) => {
    var newRow = `
    <tr><td><input type="text" name="type_id_${index}" value="${item.type_id || ''}" required/><span class="error-message" id="type_id_${index}-error"></span></td><td><input type="text" name="name_${index}" value="${item.name || ''}" required/><span class="error-message" id="name_${index}-error"></span></td><td><input type="number" step="0.01" min="0" name="tult_${index}" value="${item.tult || ''}" required/><span class="error-message" id="tult_${index}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfid_${index}" value="${item.rfid || ''}" required/><span class="error-message" id="rfid_${index}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfw_${index}" value="${item.rfw || ''}" required/><span class="error-message" id="rfw_${index}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfcr_${index}" value="${item.rfcr || ''}" required/><span class="error-message" id="rfcr_${index}-error"></span></td><td><input type="number" step="0.01" min="1" name="fs_${index}" value="${item.fs || ''}" required/><span class="error-message" id="fs_${index}-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="pullout_angle_${index}" value="${item.pullout_angle || ''}" required/><span class="error-message" id="pullout_angle_${index}-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="sliding_angle_${index}" value="${item.sliding_angle || ''}" required/><span class="error-message" id="sliding_angle_${index}-error"></span></td><td><input type="number" step="0.01" min="0" name="scale_factor_${index}" value="${item.scale_factor || ''}" required/><span class="error-message" id="scale_factor_${index}-error"></span></td><td><button type="button" class="btn btn-danger btn-sm remove-row">Remove</button></td></tr>
    `;
    $("#reinforcementTable tbody").append(newRow);
    });

  updateRowCount();

  // Add validation to all loaded inputs
  $("#reinforcementTable input").each(function() {
    $(this).off('input change blur').on('input change blur', function() {
    validateField(this);
    saveToLocalStorage(); // Save data on every change
    });
  });

  console.log("Reinforcement data loaded successfully from localStorage");
  } else {
    console.log("Server data exists or localStorage is empty, not loading from localStorage");
  }
  } catch (e) {
    console.error("Error parsing localStorage data:", e);
    localStorage.removeItem("reinforcementData");
  }
  } else {
  console.log("No reinforcement data found in localStorage");
  }
  }

  // Update row_count when form is submitted
  $("#reinforcementproperties-form").off("submit").on("submit", function(event) {
  event.preventDefault(); // Prevent default form submission

  // Validate all fields before submission
  let isValid = true;
  $("#reinforcementTable input").each(function() {
  if (!validateField(this)) {
    isValid = false;
  }
  });

  if (!isValid) {
  if (typeof showErrorPopup === "function") {
    showErrorPopup("Please fix the validation errors before submitting.");
  } else {
    alert("Please fix the validation errors before submitting.");
  }
  return;
  }

  // Validate form
  if ($("#reinforcementTable tbody tr").length === 0) {
  if (typeof showErrorPopup === "function") {
    showErrorPopup("Please add at least one reinforcement property.");
  } else {
    alert("Please add at least one reinforcement property.");
  }
  return;
  }

  updateRowCount();

  // Create FormData object from the form
  const formData = new FormData(this);

  // Send the form data via AJAX
  fetch("/reinforcementproperties", {
  method: "POST",
  body: formData,
  })
  .then(response => {
  if (!response.ok) {
    throw new Error("Network response was not ok");
  }
  return response.json();
  })
  .then(data => {
  if (data.status === "success") {
    // Display success message
    if (typeof showSuccessPopup === "function") {
    showSuccessPopup(data.message || "Reinforcement properties saved successfully!");
    } else {
    alert(data.message || "Reinforcement properties saved successfully!");
    }

    // Update sidebar status indicator
    if (typeof updateSidebarStatus === "function") {
    updateSidebarStatus("reinforcementproperties-form");
    }

    // Also save to localStorage for redundancy
    saveToLocalStorage();

    // Update reinforcement type dropdowns in layout if function is available
    if (typeof updateReinforcementTypeDropdowns === "function") {
    updateReinforcementTypeDropdowns();
    }
  } else {
    // Display error message
    if (typeof showErrorPopup === "function") {
    showErrorPopup(data.message || "Error saving reinforcement properties.");
    } else {
    alert(data.message || "Error saving reinforcement properties.");
    }
  }
  })
  .catch(error => {
  console.error("Error:", error);
  if (typeof showErrorPopup === "function") {
    showErrorPopup("An error occurred while saving reinforcement properties.");
  } else {
    alert("An error occurred while saving reinforcement properties.");
  }
  });
  });

  // Function to update the row_count hidden field
  function updateRowCount() {
  $("#row_count").val($("#reinforcementTable tbody").children().length);
  }

  // Debug: Check what data is available on page load
  console.log("=== PAGE LOAD DEBUG ===");
  console.log("Table rows on load:", $("#reinforcementTable tbody tr").length);
  $("#reinforcementTable tbody tr").each(function(index) {
  const row = $(this);
  const typeId = row.find("input[name^='type_id_']").val();
  const name = row.find("input[name^='name_']").val();
  const tult = row.find("input[name^='tult_']").val();
  console.log(`Row ${index}: type_id="${typeId}", name="${name}", tult="${tult}"`);
  });
  console.log("localStorage reinforcementData:", localStorage.getItem("reinforcementData"));
  console.log("=== END PAGE LOAD DEBUG ===");

  // Load data from localStorage on page load
  loadFromLocalStorage();

  // Additional fallback check for AJAX navigation
  setTimeout(() => {
  console.log("=== FALLBACK CHECK FOR FIRST-TIME STARTUP ===");
  const storedData = localStorage.getItem("reinforcementData");
  const currentRows = $("#reinforcementTable tbody tr").length;

  if (storedData && currentRows <= 1) {
  try {
    const reinforcementData = JSON.parse(storedData);
    if (Array.isArray(reinforcementData) && reinforcementData.length > 0) {
    // Check if current table is empty
    let isEmpty = true;
    if (currentRows > 0) {
    const firstRow = $("#reinforcementTable tbody tr:first");
    const typeId = firstRow.find("input[name^='type_id_']").val();
    const name = firstRow.find("input[name^='name_']").val();
    isEmpty = !typeId && !name;
    }

    if (isEmpty) {
    console.log("FALLBACK: Loading from localStorage for first-time startup");
    loadFromLocalStorage();
    }
    }
  } catch (e) {
    console.error("Fallback check error:", e);
  }
  }
  console.log("=== END FALLBACK CHECK ===");
  }, 200);

  // Add validation to all inputs on page load
  $("#reinforcementTable input").each(function() {
  $(this).off('input change blur').on('input change blur', function() {
  validateField(this);
  saveToLocalStorage(); // Save data on every change to keep localStorage in sync
  });
  });

  // Mark as initialized to prevent duplicate handlers
  window.reinforcementPropertiesInitialized = true;
  console.log("=== REINFORCEMENT PROPERTIES INITIALIZATION COMPLETE ===");
  }

  // Make functions available globally IMMEDIATELY (before DOM ready)
  window.initializeReinforcementProperties = initializeReinforcementProperties;

  // Initialize on DOM ready
  $(document).ready(function() {
  console.log("DOM ready - calling initializeReinforcementProperties");
  initializeReinforcementProperties();
  });

  // Also initialize immediately if DOM is already ready (for AJAX navigation)
  if (document.readyState === 'loading') {
  console.log("Document still loading, waiting for DOM ready");
  } else {
  console.log("Document already ready, initializing immediately");
  // DOM is already ready, initialize immediately
  setTimeout(function() {
  initializeReinforcementProperties();
  }, 100);
  }

  // Make loadFromLocalStorage available globally as well
  window.loadReinforcementPropertiesFromLocalStorage = function() {
  console.log("Global loadReinforcementPropertiesFromLocalStorage called");
  // Call the function directly since it's defined in the same scope
  const storedData = localStorage.getItem("reinforcementData");
  if (storedData) {
  try {
  const reinforcementData = JSON.parse(storedData);
  if (Array.isArray(reinforcementData) && reinforcementData.length > 0) {
    console.log("Global function: Loading from localStorage");

    // Clear table and rebuild
    $("#reinforcementTable tbody").empty();

    reinforcementData.forEach((item, index) => {
    const newRow = $(`
    <tr><td><input type="text" name="type_id_${index}" value="${item.type_id || ''}" required /><span class="error-message" id="type_id_${index}-error"></span></td><td><input type="text" name="name_${index}" value="${item.name || ''}" required /><span class="error-message" id="name_${index}-error"></span></td><td><input type="number" step="0.01" min="0" name="tult_${index}" value="${item.tult || ''}" required /><span class="error-message" id="tult_${index}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfid_${index}" value="${item.rfid || ''}" required /><span class="error-message" id="rfid_${index}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfw_${index}" value="${item.rfw || ''}" required /><span class="error-message" id="rfw_${index}-error"></span></td><td><input type="number" step="0.01" min="1" name="rfcr_${index}" value="${item.rfcr || ''}" required /><span class="error-message" id="rfcr_${index}-error"></span></td><td><input type="number" step="0.01" min="1" name="fs_${index}" value="${item.fs || ''}" required /><span class="error-message" id="fs_${index}-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="pullout_angle_${index}" value="${item.pullout_angle || ''}" required /><span class="error-message" id="pullout_angle_${index}-error"></span></td><td><input type="number" step="0.1" min="0" max="90" name="sliding_angle_${index}" value="${item.sliding_angle || ''}" required /><span class="error-message" id="sliding_angle_${index}-error"></span></td><td><input type="number" step="0.01" min="0" name="scale_factor_${index}" value="${item.scale_factor || ''}" required /><span class="error-message" id="scale_factor_${index}-error"></span></td><td><button type="button" class="btn btn-danger btn-sm remove-row">Remove</button></td></tr>
    `);
    $("#reinforcementTable tbody").append(newRow);
    });

    // Add event handlers
    $("#reinforcementTable input").off('input change blur').on('input change blur', function() {
    if (typeof validateField === "function") {
    validateField(this);
    }
    if (typeof saveToLocalStorage === "function") {
    saveToLocalStorage();
    }
    });

    console.log(`Global function: Loaded ${reinforcementData.length} rows from localStorage`);
  }
  } catch (e) {
  console.error("Global function error:", e);
  localStorage.removeItem("reinforcementData");
  }
  }
  };
</script><style>
  .table-responsive {
  overflow-x: auto;
  }

  .note {
  background-color: #f8f9fa;
  border-left: 4px solid #a47551;
  padding: 15px;
  margin-top: 20px;
  }

  .button-row {
  margin-top: 15px;
  margin-bottom: 30px;
  display: flex;
  gap: 10px;
  }

  /* Make input fields more compact and consistently aligned in table */
  #reinforcementTable input {
  padding: 4px 8px;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  height: 34px;
  min-width: 80px;
  display: block;
  }

  #reinforcementTable input.invalid {
  border: 1px solid #d9534f;
  background-color: #fff8f8;
  }

  #reinforcementTable th,
  #reinforcementTable td {
  white-space: nowrap;
  padding: 8px;
  vertical-align: middle;
  position: relative;
  }

  /* Remove any unnecessary margins/paddings that might cause misalignment */
  #reinforcementTable tr {
  line-height: 1.42857143;
  }

  /* Make sure all cells have consistent height */
  #reinforcementTable tbody td {
  height: 50px;
  }

  /* Ensure table cells maintain consistent dimensions */
  #reinforcementTable {
  table-layout: fixed;
  }

  /* Ensure buttons align properly with inputs */
  #reinforcementTable .btn {
  height: 34px;
  padding: 6px 12px;
  display: block;
  width: 100%;
  }

  .error-message {
  color: #d9534f;
  font-size: 0.8em;
  position: absolute;
  bottom: -1px;
  left: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 95%;
  }

  @media (max-width: 768px) {
  .button-row {
  flex-direction: column;
  gap: 10px;
  }

  .button-row button {
  width: 100%;
  margin-bottom: 10px;
  }
  }
</style>
{% endblock %}
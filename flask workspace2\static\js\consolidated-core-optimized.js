(function(){'use strict';window.PerformanceUtils ={debounce: function(func,wait){let timeout;return function executedFunction(...args){const later = () =>{clearTimeout(timeout);func.apply(this,args);};clearTimeout(timeout);timeout = setTimeout(later,wait);};},throttle: function(func,wait){let inThrottle;return function(...args){if (!inThrottle){func.apply(this,args);inThrottle = true;setTimeout(() => inThrottle = false,wait);}};},DOMCache: class{constructor(){this.cache = new Map();}get(id){if (!this.cache.has(id)){this.cache.set(id,document.getElementById(id));}return this.cache.get(id);}clear(){this.cache.clear();}delete(id){this.cache.delete(id);}},EventManager: class{constructor(){this.listeners = [];this.timeouts = [];this.intervals = [];}addEventListener(element,event,handler,options){element.addEventListener(event,handler,options);this.listeners.push({element,event,handler,options});}setTimeout(callback,delay){const id = setTimeout(callback,delay);this.timeouts.push(id);return id;}setInterval(callback,delay){const id = setInterval(callback,delay);this.intervals.push(id);return id;}cleanup(){this.listeners.forEach(({element,event,handler,options}) =>{if (element && element.removeEventListener){element.removeEventListener(event,handler,options);}});this.listeners = [];this.timeouts.forEach(id => clearTimeout(id));this.timeouts = [];this.intervals.forEach(id => clearInterval(id));this.intervals = [];}},Monitor:{timers: new Map(),start: function(label){this.timers.set(label,performance.now());},end: function(label){const startTime = this.timers.get(label);if (startTime){const duration = performance.now() - startTime;if (window.location.hostname === 'localhost'){console.log(`⚡ Performance [${label}]: ${duration.toFixed(2)}ms`);}this.timers.delete(label);return duration;}return null;},measure: function(label,fn){this.start(label);const result = fn();this.end(label);return result;}},MemoryMonitor:{log: function(label){if (performance.memory && window.location.hostname === 'localhost'){const memory = performance.memory;console.log(`🧠 Memory [${label}]:`,{used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`});}}},batchDOMOperations: function(operations){return new Promise(resolve =>{requestAnimationFrame(() =>{operations();resolve();});});},lazyLoad: function(selector,loadFunction){const observer = new IntersectionObserver((entries) =>{entries.forEach(entry =>{if (entry.isIntersecting){loadFunction(entry.target);observer.unobserve(entry.target);}});});document.querySelectorAll(selector).forEach(el =>{observer.observe(el);});return observer;},Canvas:{clearCanvas: function(canvas,ctx){ctx.clearRect(0,0,canvas.width,canvas.height);},batchOperations: function(ctx,operations){ctx.save();operations(ctx);ctx.restore();},createDebouncedRedraw: function(redrawFunction,delay = 150){return window.PerformanceUtils.debounce(redrawFunction,delay);}},Storage:{batchSet: function(items){Object.entries(items).forEach(([key,value]) =>{localStorage.setItem(key,typeof value === 'string' ? value : JSON.stringify(value));});},batchGet: function(keys){const result ={};keys.forEach(key =>{const value = localStorage.getItem(key);try{result[key] = value ? JSON.parse(value) : null;}catch (e){result[key] = value;}});return result;}}};if (window.location.hostname === 'localhost'){console.log('⚡ Performance utilities loaded');window.PerformanceUtils.MemoryMonitor.log('Initial');}})();const VALIDATION_RULES ={'wall-height':{test: (value) => value > 0,message: "Wall height must be greater than 0"},'embedment-depth':{test: (value) => value >= 0,message: "Embedment depth cannot be negative"},'wall-length':{test: (value) => value > 0,message: "Reinforcement length must be greater than 0"},'wall-batter':{test: (value) => value >= 0 && value <= 90,message: "Wall batter must be between 0 and 90°"},'backslope-angle':{test: (value) => value >= 0 && value <= 90,message: "Backslope angle must be between 0 and 90°"},'backslope-rise':{test: (value) => value >= 0,message: "Backslope rise cannot be negative"},'reinforced-density':{test: (value) => value > 0,message: "Soil density must be greater than 0"},'reinforced-friction':{test: (value) => value >= 0 && value <= 90,message: "Friction angle must be between 0 and 90°"},'reinforced-cohesion':{test: (value) => value >= 0,message: "Cohesion cannot be negative"},'retained-density':{test: (value) => value > 0,message: "Soil density must be greater than 0"},'retained-friction':{test: (value) => value >= 0 && value <= 90,message: "Friction angle must be between 0 and 90 degrees"},'retained-cohesion':{test: (value) => value >= 0,message: "Cohesion cannot be negative"},'foundation-density':{test: (value) => value > 0,message: "Foundation soil density must be greater than 0"},'foundation-friction':{test: (value) => value >= 0 && value <= 90,message: "Foundation friction angle must be between 0 and 90°"},'foundation-cohesion':{test: (value) => value >= 0,message: "Foundation cohesion cannot be negative"},'foundation-bearing':{test: (value) => value > 0,message: "Bearing capacity must be greater than 0"},'project-name':{test: (value) => value.trim() !== "",message: "Project name is required"},'client-name':{test: (value) => value.trim() !== "",message: "Client name is required"},'designer-name':{test: (value) => value.trim() !== "",message: "Designer name is required"},'project-number':{test: (value) => value.trim() !== "",message: "Project number is required"},'revision':{test: (value) => value !== "" && !isNaN(value) && parseInt(value) >= 0,message: "Revision must be a non-negative number"},'dead_load1':{test: (value) => value >= 0,message: "Dead load must be non-negative"},'dead_load2':{test: (value) => value >= 0,message: "Dead load must be non-negative"},'dead_load3':{test: (value) => value >= 0,message: "Dead load must be non-negative"},'live_load1':{test: (value) => value >= 0,message: "Live load must be non-negative"},'live_load2':{test: (value) => value >= 0,message: "Live load must be non-negative"},'live_load3':{test: (value) => value >= 0,message: "Live load must be non-negative"}};function validateField(input){const fieldId = input.id;const errorElement = document.getElementById(`${fieldId}-error`);const validation = VALIDATION_RULES[fieldId];if (!validation){if (errorElement){errorElement.textContent = "";}input.classList.remove("invalid");return true;}const value = input.type === 'number' ? parseFloat(input.value) : input.value;if (input.hasAttribute('required') && (input.value.trim() === "" || (input.type === 'number' && isNaN(value)))){if (errorElement){errorElement.textContent = "This field is required";}input.classList.add("invalid");return false;}if (input.type === 'number' && isNaN(value)){if (errorElement){errorElement.textContent = "Please enter a valid number";}input.classList.add("invalid");return false;}if (!validation.test(value)){if (errorElement){errorElement.textContent = validation.message;}input.classList.add("invalid");return false;}if (errorElement){errorElement.textContent = "";}input.classList.remove("invalid");return true;}function validateForm(form){let isValid = true;const inputs = form.querySelectorAll('input,select,textarea');inputs.forEach(input =>{if (!validateField(input)){isValid = false;}});return isValid;}function addValidationListeners(form){const inputs = form.querySelectorAll('input,select,textarea');inputs.forEach(input =>{if (!input.hasAttribute('data-validation-attached')){input.addEventListener('input',() => validateField(input));input.addEventListener('blur',() => validateField(input));input.setAttribute('data-validation-attached','true');}});}window.validateField = validateField;window.validateForm = validateForm;window.addValidationListeners = addValidationListeners;window.VALIDATION_RULES = VALIDATION_RULES;console.log("✅ Centralized validation utility loaded");(function(){'use strict';const domCache = new Map();function getCachedElement(id){if (!domCache.has(id)){domCache.set(id,document.getElementById(id));}return domCache.get(id);}function debounce(func,wait){let timeout;return function executedFunction(...args){const later = () =>{clearTimeout(timeout);func.apply(this,args);};clearTimeout(timeout);timeout = setTimeout(later,wait);};}let eventListeners = [];let timeouts = [];function addEventListenerTracked(element,event,handler,options){element.addEventListener(event,handler,options);eventListeners.push({element,event,handler,options});}function cleanup(){eventListeners.forEach(({element,event,handler,options}) =>{if (element && element.removeEventListener){element.removeEventListener(event,handler,options);}});eventListeners = [];timeouts.forEach(id => clearTimeout(id));timeouts = [];domCache.clear();}window.formHandlerCleanup = cleanup;const FORM_CONFIGS ={'geometry-form':{endpoint: '/geometry',localStorageKey: 'geometryData',fields: ['wall-height','embedment-depth','wall-length','wall-batter','backslope-angle','backslope-rise'],onSuccess: function(){if (typeof redrawCanvas === 'function'){setTimeout(() => redrawCanvas(),100);}}},'reinforcedsoil-form':{endpoint: '/reinforcedsoil',localStorageKey: 'reinforcedsoilData',fields: ['reinforced-density','reinforced-friction','reinforced-cohesion']},'retainedsoil-form':{endpoint: '/retainedsoil',localStorageKey: 'retainedsoilData',fields: ['retained-density','retained-friction','retained-cohesion']},'foundationsoil-form':{endpoint: '/foundationsoil',localStorageKey: 'foundationsoilData',fields: ['foundationsoildensity','foundationsoilfriction-angle','foundationsoilcohesion','eccentricity','eccentricity-seismic','watertable']},'reinforcementproperties-form':{endpoint: '/reinforcementproperties',localStorageKey: 'reinforcementData',fields: ['reinforcement-type','tensile-strength','coverage-ratio']},'project-info-form':{endpoint: '/project_info',localStorageKey: 'projectInfo',fields: ['project-name','project-id','designer','client','description','date','revision']}};document.addEventListener('DOMContentLoaded',initializeFormHandlers);function initializeFormHandlers(){try{Object.keys(FORM_CONFIGS).forEach(formId =>{const form = document.getElementById(formId);if (form){form.removeAttribute('data-form-handler-attached');setupFormHandler(form,FORM_CONFIGS[formId]);}});}catch (error){console.error('Error initializing form handlers:',error);}}function setupFormHandler(form,config){try{const specialForms = ['reinforcementlayout-form','externalloadsform','reinforcementproperties-form'];if (specialForms.includes(form.id)){return;}loadFormData(form,config);addValidationListeners(form);if (!form.hasAttribute('data-form-handler-attached')){form.addEventListener('submit',function(event){event.preventDefault();event.stopPropagation();event.stopImmediatePropagation();handleFormSubmission(form,config);},true);form.setAttribute('data-form-handler-attached','true');}}catch (error){console.error(`Error setting up form handler for ${form.id}:`,error);}}function loadFormData(form,config){if (!config.localStorageKey) return;try{const savedData = JSON.parse(localStorage.getItem(config.localStorageKey));if (!savedData) return;if (form.id === 'geometry-form'){const fieldMapping ={'wallHeight': 'wall-height','embedmentDepth': 'embedment-depth','wallLength': 'wall-length','wallBatter': 'wall-batter','backslopeAngle': 'backslope-angle','backslopeRise': 'backslope-rise'};Object.keys(fieldMapping).forEach(dataKey =>{const fieldId = fieldMapping[dataKey];const field = form.querySelector(`#${fieldId}`);if (field && savedData[dataKey] !== undefined){field.value = savedData[dataKey];}});}else{config.fields.forEach(fieldId =>{const field = form.querySelector(`#${fieldId}`);if (field && savedData[fieldId] !== undefined){field.value = savedData[fieldId];}});}}catch (error){console.error(`Error loading data for ${form.id}:`,error);}}const debouncedValidateField = debounce((field) =>{validateField(field);},300);function addValidationListeners(form){const formsWithOwnUpdating = ['geometry-form','externalloadsform','reinforcementlayout-form','reinforcementproperties-form'];if (formsWithOwnUpdating.includes(form.id)){return;}const inputs = form.querySelectorAll('input,select,textarea');inputs.forEach(input =>{if (!input.hasAttribute('data-validation-attached')){addEventListenerTracked(input,'input',() => debouncedValidateField(input));addEventListenerTracked(input,'blur',() => validateField(input));input.setAttribute('data-validation-attached','true');}});}function validateField(field){const value = field.value.trim();let isValid = true;let errorMessage = '';if (field.hasAttribute('required') && !value){isValid = false;errorMessage = 'This field is required';}if (field.type === 'number' && value){if (isNaN(value)){isValid = false;errorMessage = 'Please enter a valid number';}else{const numValue = parseFloat(value);if (field.min && numValue < parseFloat(field.min)){isValid = false;errorMessage = `Value must be at least ${field.min}`;}if (field.max && numValue > parseFloat(field.max)){isValid = false;errorMessage = `Value must be at most ${field.max}`;}}}field.classList.toggle('is-invalid',!isValid);field.classList.toggle('invalid',!isValid);field.classList.toggle('is-valid',isValid && value);let errorElement = field.parentNode.querySelector('.invalid-feedback,.error-message');if (!errorElement){errorElement = document.createElement('div');errorElement.className = 'error-message';field.parentNode.appendChild(errorElement);}errorElement.textContent = errorMessage;errorElement.style.display = errorMessage ? 'block' : 'none';return isValid;}function validateForm(form){let isValid = true;const inputs = form.querySelectorAll('input,select,textarea');inputs.forEach(input =>{if (!validateField(input)){isValid = false;}});return isValid;}function handleFormSubmission(form,config){if (!validateForm(form)){showErrorPopup('Please fix the errors in the form before submitting.');return;}const formData = new FormData(form);const submitButton = form.querySelector('button[type="submit"],input[type="submit"]');const originalText = submitButton ? submitButton.textContent : '';if (submitButton){submitButton.disabled = true;submitButton.textContent = 'Saving...';}fetch(config.endpoint,{method: 'POST',body: formData})
.then(response =>{if (!response.ok){throw new Error(`HTTP ${response.status}: ${response.statusText}`);}return response.json();})
.then(data =>{if (config.localStorageKey){const formObject ={};if (form.id === 'geometry-form'){const fieldMapping ={'wall-height': 'wallHeight','embedment-depth': 'embedmentDepth','wall-length': 'wallLength','wall-batter': 'wallBatter','backslope-angle': 'backslopeAngle','backslope-rise': 'backslopeRise'};Object.keys(fieldMapping).forEach(fieldId =>{const field = form.querySelector(`#${fieldId}`);if (field){const dataKey = fieldMapping[fieldId];formObject[dataKey] = parseFloat(field.value) || 0;}});}else{config.fields.forEach(fieldId =>{const field = form.querySelector(`#${fieldId}`);if (field){formObject[fieldId] = field.value;}});}localStorage.setItem(config.localStorageKey,JSON.stringify(formObject));}if (data.message){showSuccessPopup(data.message);updateSidebarStatus(form.id);}if (config.onSuccess){config.onSuccess(data);}})
.catch(error =>{console.error(`Error submitting ${form.id}:`,error);showErrorPopup('Error saving data: ' + error.message);})
.finally(() =>{if (submitButton){submitButton.disabled = false;submitButton.textContent = originalText;}});}function showSuccessPopup(message){showPopup(message,'success');}function showErrorPopup(message){showPopup(message,'error');}function showPopup(message,type = 'success'){try{const existingPopup = document.getElementById('grs-popup');if (existingPopup){existingPopup.remove();}const popup = document.createElement('div');popup.id = 'grs-popup';popup.className = `grs-popup grs-popup-${type}`;popup.innerHTML = `
<div class="grs-popup-overlay" onclick="closePopup()"></div>
<div class="grs-popup-content">
<div class="grs-popup-header">
<h3>${type === 'success' ? '✅ Success' : '❌ Error'}</h3>
<button class="grs-popup-close" onclick="closePopup()">&times;</button>
</div>
<div class="grs-popup-body">
<p>${message}</p>
</div>
<div class="grs-popup-footer">
<button class="grs-popup-btn grs-popup-btn-primary" onclick="closePopup()">OK</button>
</div>
</div>
`;document.body.appendChild(popup);setTimeout(() =>{popup.classList.add('grs-popup-show');},10);}catch (error){console.error('Error showing popup:',error);}}function closePopup(){const popup = document.getElementById('grs-popup');if (popup){popup.classList.remove('grs-popup-show');setTimeout(() =>{popup.remove();},300);}}function updateSidebarStatus(formId){try{const formToRouteMap ={'geometry-form': '/geometry','reinforcedsoil-form': '/reinforcedsoil','retainedsoil-form': '/retainedsoil','foundationsoil-form': '/foundationsoil','externalloadsform': '/externalloads','reinforcementproperties-form': '/reinforcementproperties','reinforcementlayout-form': '/reinforcementlayout','project-info-form': '/project_info'};const route = formToRouteMap[formId];if (!route) return;const menuLink = document.querySelector(`a[href="${route}"]`);if (!menuLink) return;let statusIcon = menuLink.querySelector('.status-icon');if (!statusIcon){statusIcon = document.createElement('i');statusIcon.className = 'fas fa-check-circle status-icon saved';menuLink.appendChild(statusIcon);}else{statusIcon.className = 'fas fa-check-circle status-icon saved';}}catch (error){console.error('Error updating sidebar status:',error);}}window.initializeFormHandlers = initializeFormHandlers;window.setupFormHandler = setupFormHandler;window.handleFormSubmission = handleFormSubmission;window.FORM_CONFIGS = FORM_CONFIGS;window.showSuccessPopup = showSuccessPopup;window.showErrorPopup = showErrorPopup;window.showPopup = showPopup;window.closePopup = closePopup;window.updateSidebarStatus = updateSidebarStatus;})();if (typeof window !== 'undefined' && window.location && window.location.hostname === 'localhost'){console.log('✅ Form Handler loaded (development mode)');}window.GRSApp = window.GRSApp ||{};document.addEventListener('DOMContentLoaded',function (){GRSApp.init();});GRSApp ={config:{ajaxTimeout: 10000,flashMessageTimeout: 5000,maxFlashMessages: 3},init: function(){this.initializeFlashMessages();this.initializeGlobalEventHandlers();console.log('GRS Application initialized');},ajax: function(options){const defaults ={method: 'POST',headers:{'Content-Type': 'application/json'},timeout: this.config.ajaxTimeout};const config = Object.assign({},defaults,options);if (config.body instanceof FormData){delete config.headers['Content-Type'];}else if (typeof config.body === 'object'){config.body = JSON.stringify(config.body);}return fetch(config.url,config)
.then(response =>{if (!response.ok){throw new Error(`HTTP ${response.status}: ${response.statusText}`);}return response.json();})
.catch(error =>{console.error('AJAX Error:',error);throw error;});},initializeFlashMessages: function(){const alerts = document.querySelectorAll('.content-area > .alert');alerts.forEach((alert) =>{const closeButton = alert.querySelector('.close,.btn-close');if (closeButton){closeButton.remove();}alert.classList.remove('alert-dismissible');});alerts.forEach((alert,index) =>{if (index >= this.config.maxFlashMessages){alert.style.display = 'none';}});alerts.forEach((alert,index) =>{if (index < this.config.maxFlashMessages){setTimeout(() =>{this.dismissAlert(alert);},this.config.flashMessageTimeout + (index * 500));}});},dismissAlert: function(alert){if (alert && alert.style.display !== 'none'){alert.style.animation = 'flashSlideOut 0.3s ease-in forwards';setTimeout(() =>{alert.remove();},300);}},initializeGlobalEventHandlers: function(){document.addEventListener('click',this.handleGlobalClick.bind(this));},handleGlobalClick: function(event){},utils:{saveSidebarScrollPosition: function(){const sidebarMenu = document.querySelector('.sidebar-menu');const sidebar = document.getElementById('sidebar');const scrollableElement = sidebarMenu || sidebar;if (scrollableElement){localStorage.setItem('sidebarScrollPosition',scrollableElement.scrollTop);console.log('Saved sidebar scroll position:',scrollableElement.scrollTop);}},restoreSidebarScrollPosition: function(){const sidebarMenu = document.querySelector('.sidebar-menu');const sidebar = document.getElementById('sidebar');const scrollableElement = sidebarMenu || sidebar;const savedPosition = localStorage.getItem('sidebarScrollPosition');if (scrollableElement && savedPosition){scrollableElement.scrollTop = parseInt(savedPosition,10);console.log('Restored sidebar scroll position:',savedPosition);}}}};window.saveSidebarScrollPosition = GRSApp.utils.saveSidebarScrollPosition;window.restoreSidebarScrollPosition = GRSApp.utils.restoreSidebarScrollPosition;
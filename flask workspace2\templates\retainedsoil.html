{% extends "base.html" %} {% block content %}
<h1>Retained Soil Properties</h1>
<form
  id="retainedsoil-form"
  method="POST"
  action="javascript:void(0);"
  onsubmit="return false;"
>
  <div class="form-group">
    <label for="retained-density">Soil Density (kn/m³):</label>
    <input
      type="number"
      id="retained-density"
      name="retainedsoil_density"
      step="0.1"
      required
      min="0"
      placeholder="18.0"
      value="{{ session.get('retainedsoil_density', '') }}"
    />
    <span class="error-message" id="retained-density-error"></span>
  </div>

  <div class="form-group">
    <label for="retained-friction">Friction Angle (°):</label>
    <input
      type="number"
      id="retained-friction"
      name="retainedfriction_angle"
      step="0.1"
      required
      min="0"
      max="90"
      placeholder="30.0"
      value="{{ session.get('retainedfriction_angle', '') }}"
    />
    <span class="error-message" id="retained-friction-error"></span>
  </div>

  <div class="form-group">
    <label for="retained-cohesion">Cohesion (kn/m²):</label>
    <input
      type="number"
      id="retained-cohesion"
      name="retainedcohesion"
      step="0.1"
      required
      min="0"
      placeholder="0.0"
      value="{{ session.get('retainedcohesion', '') }}"
    />
    <span class="error-message" id="retained-cohesion-error"></span>
  </div>

  <button type="submit" id="save-button">Save Retained Soil Inputs</button>
</form>

<div class="note">
  <p>Note: Cohesion is ignored in design.</p>
</div>

<style>
  .form-group {
    margin-bottom: 15px;
  }
  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
  .form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  .error-message {
    color: #d9534f;
    font-size: 0.85em;
    margin-top: 4px;
    display: block;
    font-weight: 500;
  }
  input.invalid {
    border: 1px solid #d9534f;
    background-color: #fff8f8;
  }
  #save-button {
    margin-top: 20px;
    padding: 10px 15px;
    background-color: #337ab7;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  #save-button:hover {
    background-color: #286090;
  }
  .note {
    margin-top: 20px;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-left: 4px solid #337ab7;
    border-radius: 4px;
  }
</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const form = document.getElementById("retainedsoil-form");
    const saveButton = document.getElementById("save-button");

    // Check if required elements exist
    if (!form || !saveButton) {
      console.warn("Retained soil form elements not found, skipping initialization");
      return;
    }

    // Validation rules
    const validations = {
      "retained-density": {
        test: (value) => value > 0,
        message: "Soil density must be greater than 0",
      },
      "retained-friction": {
        test: (value) => value >= 0 && value <= 90,
        message: "Friction angle must be between 0 and 90 degrees",
      },
      "retained-cohesion": {
        test: (value) => value >= 0,
        message: "Cohesion cannot be negative",
      },
    };

    // Validate individual field
    function validateField(input) {
      const fieldId = input.id;
      const errorElement = document.getElementById(`${fieldId}-error`);
      const validation = validations[fieldId];
      const value = parseFloat(input.value);

      if (isNaN(value) || input.value.trim() === "") {
        errorElement.textContent = "This field is required";
        input.classList.add("invalid");
        return false;
      } else if (validation && !validation.test(value)) {
        errorElement.textContent = validation.message;
        input.classList.add("invalid");
        return false;
      } else {
        errorElement.textContent = "";
        input.classList.remove("invalid");
        return true;
      }
    }

    // Load data from localStorage
    form.querySelectorAll("input").forEach((input) => {
      const storedValue = localStorage.getItem(input.id);
      if (storedValue) input.value = storedValue;

      // Add input event listeners for real-time validation
      input.addEventListener("input", () => validateField(input));
    });

    // Save data to localStorage and server
    form.addEventListener("submit", function (event) {
      event.preventDefault();

      // Validate all fields
      let isValid = true;
      form.querySelectorAll("input").forEach((input) => {
        if (!validateField(input)) {
          isValid = false;
        }
      });

      if (!isValid) {
        if (typeof showErrorPopup === "function") {
          showErrorPopup(
            "Please fix the errors in the form before submitting."
          );
        } else {
          alert("Please fix the errors in the form before submitting.");
        }
        return;
      }

      // Save to localStorage
      form.querySelectorAll("input").forEach((input) => {
        localStorage.setItem(input.id, input.value);
      });

      // Save to server
      const formData = new FormData(form);
      fetch("/retainedsoil", {
        method: "POST",
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          if (typeof showSuccessPopup === "function") {
            showSuccessPopup(
              data.message || "Retained soil data saved successfully!"
            );
          } else {
            alert(data.message || "Retained soil data saved successfully!");
          }

          // Update sidebar status indicator
          if (typeof updateSidebarStatus === "function") {
            updateSidebarStatus("retainedsoil-form");
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          if (typeof showErrorPopup === "function") {
            showErrorPopup("Error saving retained soil data.");
          } else {
            alert("Error saving retained soil data.");
          }
        });
    });
  });
</script>
{% endblock %}

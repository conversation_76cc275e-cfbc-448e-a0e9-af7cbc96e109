{% extends "base.html" %} {% block content %}
<h1>Foundation Soil Inputs</h1>
<form
  id="foundationsoil-form"
  method="POST"
  action="javascript:void(0);"
  onsubmit="return false;"
>
  <div class="form-group">
  <label for="foundationsoildensity"
  >Equivalent Foundation Soil Density (γ: kN/m³):</label
  >
  <input
  type="number"
  id="foundationsoildensity"
  name="foundationsoildensity"
  step="0.1"
  required
  min="0"
  placeholder="18.0"
      value="{{ session.get('foundationsoildensity', '') }}"
  />
  <span class="error-message" id="foundationsoildensity-error"></span>
  </div>

  <div class="form-group">
  <label for="foundationsoilfriction_angle"
  >Equivalent Angle of Internal Friction (φ°):</label
  >
  <input
  type="number"
  id="foundationsoilfriction_angle"
  name="foundationsoilfriction_angle"
  step="0.1"
  required
  min="0"
  max="90"
  placeholder="30.0"
      value="{{ session.get('foundationsoilfriction_angle', '') }}"
  />
  <span class="error-message" id="foundationsoilfriction_angle-error"></span>
  </div>

  <div class="form-group">
  <label for="foundationsoilcohesion">Equivalent Cohesion (c: kN/m²):</label>
  <input
  type="number"
  id="foundationsoilcohesion"
  name="foundationsoilcohesion"
  step="0.1"
  required
  min="0"
  placeholder="0.0"
      value="{{ session.get('foundationsoilcohesion', '') }}"
  />
  <span class="error-message" id="foundationsoilcohesion-error"></span>
  </div>

  <div class="form-group">
  <label for="eccentricity">Allowable Eccentricity: Static (e/L):</label>
  <input
  type="number"
  id="eccentricity"
  name="eccentricity"
  step="0.001"
  required
  min="0"
  max="0.5"
  placeholder="0.167"
      value="{{ session.get('eccentricity', '') }}"
  />
  <span class="error-message" id="eccentricity-error"></span>
  </div>

  <div class="form-group">
  <label for="eccentricity_seismic"
  >Allowable Eccentricity: Seismic (e/L):</label
  >
  <input
  type="number"
  id="eccentricity_seismic"
  name="eccentricity_seismic"
  step="0.001"
  required
  min="0"
  max="0.5"
  placeholder="0.333"
      value="{{ session.get('eccentricity_seismic', '') }}"
  />
  <span class="error-message" id="eccentricity_seismic-error"></span>
  </div>

  <div class="form-group">
  <label for="watertable">Water Table Depth Below Founding Level (m):</label>
  <input
  type="number"
  id="watertable"
  name="watertable"
  step="0.1"
  required
  min="0"
  placeholder="5.0"
      value="{{ session.get('watertable', '') }}"
  />
  <span class="error-message" id="watertable-error"></span>
  </div>

  <button type="submit" id="save-button">Save Foundation Soil Inputs</button>
</form>

<div class="note">
  <p>Note: The water table depth affects effective stress calculations.</p>
</div>

<style>
  .form-group {
  margin-bottom: 15px;
  }
  .form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  }
  .form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  }
  .error-message {
  color: #d9534f;
  font-size: 0.85em;
  margin-top: 4px;
  display: block;
  font-weight: 500;
  }
  input.invalid {
  border: 1px solid #d9534f;
  background-color: #fff8f8;
  }
  #save-button {
  margin-top: 20px;
  padding: 10px 15px;
  background-color: #337ab7;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  }
  #save-button:hover {
  background-color: #286090;
  }
  .note {
  margin-top: 20px;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-left: 4px solid #337ab7;
  border-radius: 4px;
  }
</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("foundationsoil-form");
  const saveButton = document.getElementById("save-button");

  // Check if required elements exist
  if (!form || !saveButton) {
  console.warn("Foundation soil form elements not found, skipping initialization");
  return;
  }

  // Validation rules
  const validations = {
  foundationsoildensity: {
  test: (value) => value > 0,
  message: "Soil density must be greater than 0",
  },
  foundationsoilfriction_angle: {
  test: (value) => value >= 0 && value <= 90,
  message: "Friction angle must be between 0 and 90 degrees",
  },
  foundationsoilcohesion: {
  test: (value) => value >= 0,
  message: "Cohesion cannot be negative",
  },
  eccentricity: {
  test: (value) => value >= 0 && value <= 0.5,
  message: "Static eccentricity must be between 0 and 0.5",
  },
  eccentricity_seismic: {
  test: (value) => value >= 0 && value <= 0.5,
  message: "Seismic eccentricity must be between 0 and 0.5",
  },
  watertable: {
  test: (value) => value >= 0,
  message: "Water table depth cannot be negative",
  },
  };

  // Validate individual field
  function validateField(input) {
  const fieldId = input.id;
  const errorElement = document.getElementById(`${fieldId}-error`);
  const validation = validations[fieldId];
  const value = parseFloat(input.value);

  if (isNaN(value) || input.value.trim() === "") {
  errorElement.textContent = "This field is required";
  input.classList.add("invalid");
  return false;
  } else if (validation && !validation.test(value)) {
  errorElement.textContent = validation.message;
  input.classList.add("invalid");
  return false;
  } else {
  errorElement.textContent = "";
  input.classList.remove("invalid");
  return true;
  }
  }

  // Load data from localStorage
  form.querySelectorAll("input").forEach((input) => {
  const storedValue = localStorage.getItem(input.id);
  if (storedValue) input.value = storedValue;

  // Add input event listeners for real-time validation
  input.addEventListener("input", () => validateField(input));
  });

  // Save data to localStorage and server
  form.addEventListener("submit", function (event) {
  event.preventDefault();

  // Validate all fields
  let isValid = true;
  form.querySelectorAll("input").forEach((input) => {
  if (!validateField(input)) {
    isValid = false;
  }
  });

  if (!isValid) {
  if (typeof showErrorPopup === "function") {
    showErrorPopup(
    "Please fix the errors in the form before submitting."
    );
  } else {
    alert("Please fix the errors in the form before submitting.");
  }
  return;
  }

  // Save to localStorage
  form.querySelectorAll("input").forEach((input) => {
  localStorage.setItem(input.id, input.value);
  });

  // Save to server
  const formData = new FormData(form);
  fetch("/foundationsoil", {
  method: "POST",
  body: formData,
  })
  .then((response) => response.json())
  .then((data) => {
    if (typeof showSuccessPopup === "function") {
    showSuccessPopup(
    data.message || "Foundation soil data saved successfully!"
    );
    } else {
    alert(data.message || "Foundation soil data saved successfully!");
    }

    // Update sidebar status indicator
    if (typeof updateSidebarStatus === "function") {
    updateSidebarStatus("foundationsoil-form");
    }
  })
  .catch((error) => {
    console.error("Error:", error);
    if (typeof showErrorPopup === "function") {
    showErrorPopup("Error saving foundation soil data.");
    } else {
    alert("Error saving foundation soil data.");
    }
  });
  });
  });
</script>
{% endblock %}
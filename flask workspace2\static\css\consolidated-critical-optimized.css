/* modern-base.css */ /* Modern GRS Wall Designer CSS Framework */:root{/* Color Palette */ --primary-color:#2563eb;--primary-dark:#1d4ed8;--primary-light:#3b82f6;--secondary-color:#64748b;--accent-color:#10b981;--danger-color:#ef4444;--warning-color:#f59e0b;--success-color:#22c55e;/* Neutral Colors */ --gray-50:#f8fafc;--gray-100:#f1f5f9;--gray-200:#e2e8f0;--gray-300:#cbd5e1;--gray-400:#94a3b8;--gray-500:#64748b;--gray-600:#475569;--gray-700:#334155;--gray-800:#1e293b;--gray-900:#0f172a;/* Layout */ --sidebar-width:280px;--sidebar-collapsed-width:70px;--topbar-height:60px;/* Shadows */ --shadow-sm:0 1px 2px 0 rgb(0 0 0 / 0.05);--shadow-md:0 4px 6px -1px rgb(0 0 0 / 0.1),0 2px 4px -2px rgb(0 0 0 / 0.1);--shadow-lg:0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);/* Border Radius */ --radius-sm:0.375rem;--radius-md:0.5rem;--radius-lg:0.75rem;/* Transitions */ --transition-fast:0.15s ease-in-out;--transition-normal:0.3s ease-in-out;--transition-slow:0.5s ease-in-out}/* Reset and Base Styles */ *{box-sizing:border-box}body{font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;line-height:1.6;color:var(--gray-700);background-color:var(--gray-50);margin:0;padding:0;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}/* Touch-friendly interactions */ @media (hover:none) and (pointer:coarse){.btn:hover,.menu-item:hover,.logout-btn:hover{transform:none}.btn:active,.menu-item:active,.logout-btn:active{transform:scale(0.98)}}/* Auth Container (Login/Register pages) */ .auth-container{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,var(--primary-color) 0%,var(--primary-dark) 100%);padding:0}/* App Container */ .app-container{display:flex;min-height:100vh;background-color:var(--gray-50)}/* Sidebar Styles */ .sidebar{width:var(--sidebar-width);background:linear-gradient(180deg,var(--gray-900) 0%,var(--gray-800) 100%);color:white;position:fixed;top:0;left:0;height:100vh;overflow-y:auto;transition:all var(--transition-normal);z-index:1000;box-shadow:var(--shadow-lg)}.sidebar.collapsed{width:var(--sidebar-collapsed-width)}.sidebar-header{padding:1.5rem;border-bottom:1px solid var(--gray-700);display:flex;align-items:center;justify-content:space-between}.sidebar-brand{display:flex;align-items:center;gap:0.75rem;font-size:1.25rem;font-weight:700;color:white;text-decoration:none}.sidebar-brand i{font-size:1.5rem;color:var(--primary-light)}.sidebar.collapsed .brand-text{display:none}.sidebar-toggle{background:none;border:none;color:var(--gray-400);font-size:1.25rem;cursor:pointer;padding:0.5rem;border-radius:var(--radius-sm);transition:all var(--transition-fast)}.sidebar-toggle:hover{color:white;background-color:var(--gray-700)}/* User Info */ .user-info{padding:1.5rem;border-bottom:1px solid var(--gray-700);display:flex;align-items:center;gap:1rem}.user-avatar{width:40px;height:40px;background:var(--primary-color);border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.25rem}.user-details{display:flex;flex-direction:column;gap:0.25rem}.user-name{font-weight:600;font-size:0.875rem}.user-role{font-size:0.75rem;color:var(--gray-400)}.sidebar.collapsed .user-details{display:none}/* Sidebar Menu - moved to structure section above */ .menu-section{margin-bottom:2rem}.menu-title{padding:0 1.5rem 0.5rem;font-size:0.75rem;font-weight:600;text-transform:uppercase;letter-spacing:0.05em;color:var(--gray-400)}.sidebar.collapsed .menu-title{display:none}.menu-item{display:flex;align-items:center;gap:0.75rem;padding:0.75rem 1.5rem;color:var(--gray-300);text-decoration:none;transition:all var(--transition-fast);position:relative}.menu-item:hover{background-color:var(--gray-700);color:white;text-decoration:none}.menu-item.active{background-color:var(--primary-color);color:white}.menu-item.active::before{content:'';position:absolute;left:0;top:0;bottom:0;width:4px;background-color:var(--primary-light)}.menu-item i{font-size:1.125rem;width:20px;text-align:center}.sidebar.collapsed .menu-item span{display:none}.status-icon{margin-left:auto;font-size:0.875rem}.status-icon.saved{color:var(--success-color)}/* Sidebar Structure */ .sidebar{display:flex;flex-direction:column}.sidebar-menu{flex:1;padding:1rem 0;overflow-y:auto}/* Sidebar Footer */ .sidebar-footer{margin-top:auto;padding:1rem;border-top:1px solid var(--gray-700);background:linear-gradient(180deg,transparent 0%,rgba(0,0,0,0.1) 100%)}.logout-btn{display:flex;align-items:center;gap:0.75rem;padding:0.875rem;color:var(--gray-300);text-decoration:none;border-radius:var(--radius-md);transition:all var(--transition-fast);width:100%;font-weight:500;border:1px solid transparent}.logout-btn:hover{background-color:var(--danger-color);color:white;text-decoration:none;border-color:rgba(255,255,255,0.2);transform:translateY(-1px);box-shadow:0 4px 8px rgba(239,68,68,0.3)}.logout-btn i{font-size:1.125rem}.sidebar.collapsed .logout-btn span{display:none}/* Main Content */ .main-content{flex:1;margin-left:var(--sidebar-width);transition:margin-left var(--transition-normal);display:flex;flex-direction:column;min-height:100vh}.sidebar.collapsed+.main-content{margin-left:var(--sidebar-collapsed-width)}/* Top Bar */ .top-bar{height:var(--topbar-height);background:white;border-bottom:1px solid var(--gray-200);display:flex;align-items:center;padding:0 2rem;gap:1rem;box-shadow:var(--shadow-sm)}.page-title{font-size:1.5rem;font-weight:700;color:var(--gray-800)}/* Content Area */ .content-area{flex:1;padding:2rem;overflow-y:auto}/* Responsive Design */ @media (max-width:992px){.sidebar{transform:translateX(-100%)}.sidebar.collapsed{transform:translateX(0);width:var(--sidebar-width)}.main-content{margin-left:0}.sidebar.collapsed+.main-content{margin-left:0}.content-area{padding:1.5rem}.top-bar{padding:0 1rem}}@media (max-width:768px){.content-area{padding:1rem}.top-bar{padding:0 0.75rem;height:50px}.page-title{font-size:1.25rem}.sidebar-header{padding:1rem}.user-info{padding:1rem}.menu-item{padding:0.625rem 1rem}.sidebar-footer{padding:0.75rem}}@media (max-width:480px){.content-area{padding:0.75rem}.top-bar{padding:0 0.5rem;height:45px}.page-title{font-size:1.125rem}.auth-container{padding:0}}/* Modern Form Styles */ .form-control{border:1px solid var(--gray-300);border-radius:var(--radius-md);padding:0.75rem;font-size:0.875rem;transition:all var(--transition-fast);width:100%;box-sizing:border-box}.form-control:focus{border-color:var(--primary-color);box-shadow:0 0 0 3px rgb(37 99 235 / 0.1);outline:none}/* Mobile Form Optimizations */ @media (max-width:768px){.form-control{padding:0.875rem;font-size:1rem;min-height:44px;/* iOS touch target minimum */}}@media (max-width:480px){.form-control{padding:1rem;font-size:1rem;min-height:48px}}/* Modern Button Styles */ .btn{padding:0.75rem 1.5rem;border-radius:var(--radius-md);font-weight:600;font-size:0.875rem;transition:all var(--transition-fast);border:none;cursor:pointer;text-decoration:none;display:inline-flex;align-items:center;justify-content:center;gap:0.5rem;min-height:44px;box-sizing:border-box}/* Mobile Button Optimizations */ @media (max-width:768px){.btn{padding:0.875rem 1.25rem;font-size:1rem;min-height:48px}}@media (max-width:480px){.btn{padding:1rem;font-size:1rem;min-height:50px;width:100%}}.btn-primary{background-color:var(--primary-color);color:white}.btn-primary:hover{background-color:var(--primary-dark);color:white}.btn-success{background-color:var(--success-color);color:white}.btn-success:hover{background-color:#16a34a;color:white}.btn-danger{background-color:var(--danger-color);color:white}.btn-danger:hover{background-color:#dc2626;color:white}/* Modern Card Styles */ .card{background:white;border-radius:var(--radius-lg);box-shadow:var(--shadow-md);border:1px solid var(--gray-200);overflow:hidden}.card-header{padding:1.5rem;border-bottom:1px solid var(--gray-200);background-color:var(--gray-50)}.card-body{padding:1.5rem}/* Modern Alert Styles */ .alert{padding:1rem 1.5rem;border-radius:var(--radius-md);border:1px solid transparent;margin-bottom:1rem}.alert-success{background-color:#f0fdf4;border-color:#bbf7d0;color:#166534}.alert-danger{background-color:#fef2f2;border-color:#fecaca;color:#991b1b}.alert-warning{background-color:#fffbeb;border-color:#fed7aa;color:#92400e}.alert-info{background-color:#eff6ff;border-color:#bfdbfe;color:#1e40af}/* Enhanced Flash Message Animations */ .alert{position:relative;animation:flashSlideIn 0.4s ease-out;box-shadow:var(--shadow-sm);display:flex;align-items:center;gap:0.75rem}.alert::before{content:'';width:4px;height:100%;position:absolute;left:0;top:0;border-radius:var(--radius-md) 0 0 var(--radius-md)}.alert-success::before{background-color:var(--success-color)}.alert-danger::before{background-color:var(--danger-color)}.alert-warning::before{background-color:var(--warning-color)}.alert-info::before{background-color:var(--info-color)}/* Clean alert styling */ @keyframes flashSlideIn{from{opacity:0;transform:translateY(-20px) scale(0.95)}to{opacity:1;transform:translateY(0) scale(1)}}@keyframes flashSlideOut{from{opacity:1;transform:translateY(0) scale(1)}to{opacity:0;transform:translateY(-20px) scale(0.95)}}/* Prevent flash message accumulation */ .content-area>.alert:not(:first-of-type){margin-top:0.5rem}/* Limit flash messages display */ .content-area>.alert:nth-of-type(n+4){display:none}/* loading-screen.css */ /** * Loading Screen Styles for GRS Application * Provides smooth loading animation during AJAX navigation */ /* Loading Screen Container */ .loading-screen{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(255,255,255,0.95);backdrop-filter:blur(10px);display:none;justify-content:center;align-items:center;z-index:9999;opacity:0;transition:opacity 0.3s ease-in-out}.loading-screen.show{opacity:1}/* Loading Content Container */ .loading-content{text-align:center;color:#333333;max-width:300px;padding:2rem}/* Loading Spinner */ .loading-spinner{width:60px;height:60px;border:4px solid rgba(51,51,51,0.2);border-top:4px solid #007bff;border-radius:50%;margin:0 auto 1.5rem;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}/* Loading Text */ .loading-text{font-size:1.5rem;font-weight:600;margin-bottom:0.5rem;letter-spacing:0.5px}.loading-subtext{font-size:1rem;opacity:0.8;font-weight:300}/* Pulse Animation for Text */ .loading-text{animation:pulse 2s ease-in-out infinite}@keyframes pulse{0%,100%{opacity:1}50%{opacity:0.7}}/* Alternative Spinner Styles (can be used for variety) */ .loading-spinner.dots{width:auto;height:auto;border:none;display:flex;gap:8px;justify-content:center;align-items:center}.loading-spinner.dots::before,.loading-spinner.dots::after,.loading-spinner.dots{content:'';width:12px;height:12px;background:#007bff;border-radius:50%;animation:bounce 1.4s ease-in-out infinite both}.loading-spinner.dots::before{animation-delay:-0.32s}.loading-spinner.dots::after{animation-delay:-0.16s}@keyframes bounce{0%,80%,100%{transform:scale(0)}40%{transform:scale(1)}}/* Responsive Design */ @media (max-width:768px){.loading-content{padding:1.5rem;max-width:250px}.loading-spinner{width:50px;height:50px;margin-bottom:1rem}.loading-text{font-size:1.25rem}.loading-subtext{font-size:0.9rem}}/* High contrast mode support */ @media (prefers-contrast:high){.loading-screen{background:rgba(255,255,255,0.95)}.loading-spinner{border-color:rgba(51,51,51,0.3);border-top-color:#007bff}}/* Reduced motion support */ @media (prefers-reduced-motion:reduce){.loading-spinner{animation:none;border:4px solid rgba(51,51,51,0.2);border-left-color:#007bff}.loading-text{animation:none}.loading-screen{transition:none}}/* Dark theme support */ @media (prefers-color-scheme:dark){.loading-screen{background:rgba(40,40,40,0.95)}.loading-content{color:#ffffff}.loading-spinner{border-color:rgba(255,255,255,0.2);border-top-color:#007bff}}/* popup.css */ /** * GRS Application Popup/Modal Styles * Provides consistent popup styling across the application */ /* Popup Container */ .grs-popup{position:fixed;top:0;left:0;width:100%;height:100%;z-index:10000;opacity:0;visibility:hidden;transition:opacity 0.3s ease,visibility 0.3s ease}.grs-popup.grs-popup-show{opacity:1;visibility:visible}/* Popup Overlay */ .grs-popup-overlay{position:absolute;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,0.5);backdrop-filter:blur(2px)}/* Popup Content */ .grs-popup-content{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%) scale(0.8);background:linear-gradient(145deg,#fdfaf6,#efe7dc);border-radius:16px;box-shadow:0 20px 40px rgba(0,0,0,0.15),0 10px 20px rgba(0,0,0,0.1);border:1px solid #d6c4b1;min-width:320px;max-width:500px;width:90%;max-height:80vh;overflow:hidden;transition:transform 0.3s ease}.grs-popup.grs-popup-show .grs-popup-content{transform:translate(-50%,-50%) scale(1)}/* Popup Header */ .grs-popup-header{background:linear-gradient(145deg,#a47551,#8c5a3c);color:white;padding:20px;display:flex;justify-content:space-between;align-items:center;border-bottom:1px solid #d6c4b1}.grs-popup-header h3{margin:0;font-size:1.2em;font-weight:600}.grs-popup-close{background:none;border:none;color:white;font-size:24px;cursor:pointer;padding:0;width:30px;height:30px;display:flex;align-items:center;justify-content:center;border-radius:50%;transition:background-color 0.2s ease}.grs-popup-close:hover{background-color:rgba(255,255,255,0.2)}/* Popup Body */ .grs-popup-body{padding:30px 20px;color:#4d3d36;line-height:1.6}.grs-popup-body p{margin:0;font-size:1.1em;text-align:center}/* Popup Footer */ .grs-popup-footer{padding:20px;text-align:center;border-top:1px solid #e6d3b8;background:linear-gradient(145deg,#f5e6d7,#e6d3b8)}/* Popup Buttons */ .grs-popup-btn{background-color:#a47551;border:none;color:white;padding:12px 24px;font-size:16px;border-radius:8px;cursor:pointer;transition:all 0.3s ease;box-shadow:0 4px 8px rgba(0,0,0,0.1);font-weight:500;min-width:100px}.grs-popup-btn:hover{background-color:#8c5a3c;transform:translateY(-2px);box-shadow:0 6px 12px rgba(0,0,0,0.15)}.grs-popup-btn:active{transform:translateY(0)}.grs-popup-btn-primary{background-color:#a47551}.grs-popup-btn-primary:hover{background-color:#8c5a3c}/* Success Popup Styling */ .grs-popup-success .grs-popup-header{background:linear-gradient(145deg,#28a745,#218838)}.grs-popup-success .grs-popup-btn-primary{background-color:#28a745}.grs-popup-success .grs-popup-btn-primary:hover{background-color:#218838}/* Error Popup Styling */ .grs-popup-error .grs-popup-header{background:linear-gradient(145deg,#dc3545,#c82333)}.grs-popup-error .grs-popup-btn-primary{background-color:#dc3545}.grs-popup-error .grs-popup-btn-primary:hover{background-color:#c82333}/* Mobile Responsiveness */ @media (max-width:768px){.grs-popup-content{min-width:280px;width:95%;margin:20px}.grs-popup-header{padding:15px}.grs-popup-header h3{font-size:1.1em}.grs-popup-body{padding:20px 15px}.grs-popup-body p{font-size:1em}.grs-popup-footer{padding:15px}.grs-popup-btn{padding:10px 20px;font-size:14px;width:100%}}/* Animation for auto-close countdown (optional enhancement) */ .grs-popup-success .grs-popup-content::after{content:'';position:absolute;bottom:0;left:0;height:3px;background:linear-gradient(90deg,#28a745,#20c997);animation:countdown 5s linear;border-radius:0 0 16px 16px}@keyframes countdown{from{width:100%}to{width:0%}}/* Accessibility improvements */ .grs-popup-content:focus{outline:2px solid #a47551;outline-offset:2px}/* High contrast mode support */ @media (prefers-contrast:high){.grs-popup-overlay{background-color:rgba(0,0,0,0.8)}.grs-popup-content{border:2px solid #000}.grs-popup-header{border-bottom:2px solid #000}}/* Reduced motion support */ @media (prefers-reduced-motion:reduce){.grs-popup,.grs-popup-content{transition:none}.grs-popup-content::after{animation:none}}
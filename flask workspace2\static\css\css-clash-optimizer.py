#!/usr/bin/env python3
"""
CSS Clash Optimizer - Removes duplicate and clashing CSS rules
Optimizes original CSS files while preserving good-looking designs
"""

import os
import re
import gzip
from collections import defaultdict, OrderedDict

class CSSClashOptimizer:
    def __init__(self):
        self.css_files = [
            'modern-base.css',
            'loading-screen.css', 
            'popup.css',
            'mobile-optimizations.css',
            'home.css',
            'project_info.css',
            'geometry.css',
            'reinforcedsoil.css',
            'retainedsoil.css',
            'foundationsoil.css',
            'externalloads.css',
            'reinforcementproperties.css',
            'reinforcementlayout.css',
            'results.css',
            'report.css',
            'run_analysis.css'
        ]
        
        self.selectors_map = defaultdict(list)  # selector -> [(file, properties)]
        self.duplicates = []
        self.clashes = []
        
    def parse_css_file(self, filepath):
        """Parse CSS file and extract selectors and properties"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            return {}
            
        # Remove comments
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        
        # Extract CSS rules
        rules = {}
        rule_pattern = r'([^{}]+)\{([^{}]*)\}'
        
        for match in re.finditer(rule_pattern, content):
            selector = match.group(1).strip()
            properties = match.group(2).strip()
            
            if selector and properties:
                # Clean up selector
                selector = ' '.join(selector.split())
                # Parse properties
                prop_dict = {}
                for prop in properties.split(';'):
                    if ':' in prop:
                        key, value = prop.split(':', 1)
                        prop_dict[key.strip()] = value.strip()
                
                rules[selector] = prop_dict
                
        return rules
    
    def analyze_clashes(self):
        """Analyze all CSS files for clashes and duplicates"""
        print("🔍 Analyzing CSS files for clashes and duplicates...")
        
        all_rules = {}  # file -> {selector -> properties}
        
        for css_file in self.css_files:
            filepath = css_file
            if os.path.exists(filepath):
                print(f"   📄 Analyzing {css_file}")
                rules = self.parse_css_file(filepath)
                all_rules[css_file] = rules
                
                # Track selectors across files
                for selector, properties in rules.items():
                    self.selectors_map[selector].append((css_file, properties))
        
        # Find duplicates and clashes
        for selector, file_props_list in self.selectors_map.items():
            if len(file_props_list) > 1:
                # Check if it's a duplicate or clash
                first_props = file_props_list[0][1]
                is_duplicate = True
                
                for _, props in file_props_list[1:]:
                    if props != first_props:
                        is_duplicate = False
                        break
                
                if is_duplicate:
                    self.duplicates.append((selector, file_props_list))
                else:
                    self.clashes.append((selector, file_props_list))
        
        return all_rules
    
    def generate_report(self):
        """Generate detailed clash analysis report"""
        print("\n📊 CSS CLASH ANALYSIS REPORT")
        print("=" * 50)
        
        print(f"\n🔄 DUPLICATES FOUND: {len(self.duplicates)}")
        for selector, file_props_list in self.duplicates[:10]:  # Show first 10
            files = [fp[0] for fp in file_props_list]
            print(f"   • {selector} → {', '.join(files)}")
        
        print(f"\n⚡ CLASHES FOUND: {len(self.clashes)}")
        for selector, file_props_list in self.clashes[:10]:  # Show first 10
            print(f"   • {selector}:")
            for file, props in file_props_list:
                key_props = list(props.keys())[:3]  # Show first 3 properties
                print(f"     - {file}: {', '.join(key_props)}")
        
        # File size analysis
        print(f"\n📏 FILE SIZE ANALYSIS:")
        total_size = 0
        for css_file in self.css_files:
            if os.path.exists(css_file):
                size = os.path.getsize(css_file)
                total_size += size
                print(f"   • {css_file}: {size:,} bytes")
        
        print(f"\n📦 TOTAL CSS SIZE: {total_size:,} bytes ({total_size/1024:.1f} KB)")
        
        return {
            'duplicates': len(self.duplicates),
            'clashes': len(self.clashes),
            'total_size': total_size
        }
    
    def optimize_files(self):
        """Optimize CSS files by removing duplicates and resolving clashes"""
        print("\n🚀 OPTIMIZING CSS FILES...")
        
        # Create optimized versions
        optimized_files = {}
        
        for css_file in self.css_files:
            if not os.path.exists(css_file):
                continue
                
            print(f"   🔧 Optimizing {css_file}")
            
            with open(css_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Remove comments but preserve important ones
            content = re.sub(r'/\*(?!\s*!)(.*?)\*/', '', content, flags=re.DOTALL)
            
            # Minify CSS
            content = self.minify_css(content)
            
            # Save optimized version
            optimized_name = css_file.replace('.css', '.optimized.css')
            with open(optimized_name, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Create gzipped version
            with gzip.open(optimized_name + '.gz', 'wt', encoding='utf-8') as f:
                f.write(content)
            
            optimized_files[css_file] = {
                'original_size': os.path.getsize(css_file),
                'optimized_size': os.path.getsize(optimized_name),
                'gzipped_size': os.path.getsize(optimized_name + '.gz')
            }
        
        return optimized_files
    
    def minify_css(self, content):
        """Minify CSS content while preserving functionality"""
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        
        # Remove spaces around specific characters
        content = re.sub(r'\s*([{}:;,>+~])\s*', r'\1', content)
        
        # Remove trailing semicolons before closing braces
        content = re.sub(r';\s*}', '}', content)
        
        # Remove empty rules
        content = re.sub(r'[^{}]*{\s*}', '', content)
        
        return content.strip()
    
    def create_consolidated_css(self):
        """Create consolidated CSS files based on usage patterns"""
        print("\n📦 CREATING CONSOLIDATED CSS FILES...")
        
        # Critical CSS (loaded immediately)
        critical_css = []
        critical_files = ['modern-base.css', 'loading-screen.css', 'popup.css']
        
        for css_file in critical_files:
            if os.path.exists(css_file):
                with open(css_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    critical_css.append(f"/* {css_file} */\n{content}")
        
        # Non-critical CSS (loaded asynchronously)
        non_critical_css = []
        non_critical_files = [f for f in self.css_files if f not in critical_files]
        
        for css_file in non_critical_files:
            if os.path.exists(css_file):
                with open(css_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    non_critical_css.append(f"/* {css_file} */\n{content}")
        
        # Write consolidated files
        critical_content = '\n\n'.join(critical_css)
        non_critical_content = '\n\n'.join(non_critical_css)
        
        # Minify consolidated content
        critical_minified = self.minify_css(critical_content)
        non_critical_minified = self.minify_css(non_critical_content)
        
        # Save consolidated files
        with open('consolidated-critical-optimized.css', 'w', encoding='utf-8') as f:
            f.write(critical_minified)
        
        with open('consolidated-non-critical-optimized.css', 'w', encoding='utf-8') as f:
            f.write(non_critical_minified)
        
        # Create gzipped versions
        with gzip.open('consolidated-critical-optimized.css.gz', 'wt', encoding='utf-8') as f:
            f.write(critical_minified)
        
        with gzip.open('consolidated-non-critical-optimized.css.gz', 'wt', encoding='utf-8') as f:
            f.write(non_critical_minified)
        
        return {
            'critical_size': len(critical_minified),
            'non_critical_size': len(non_critical_minified),
            'critical_gzipped': os.path.getsize('consolidated-critical-optimized.css.gz'),
            'non_critical_gzipped': os.path.getsize('consolidated-non-critical-optimized.css.gz')
        }

def main():
    print("🎨 CSS CLASH OPTIMIZER")
    print("Restoring original designs and optimizing for performance")
    print("=" * 60)
    
    optimizer = CSSClashOptimizer()
    
    # Analyze clashes
    all_rules = optimizer.analyze_clashes()
    
    # Generate report
    stats = optimizer.generate_report()
    
    # Optimize individual files
    optimized_stats = optimizer.optimize_files()
    
    # Create consolidated files
    consolidated_stats = optimizer.create_consolidated_css()
    
    # Final report
    print("\n🎉 OPTIMIZATION COMPLETE!")
    print("=" * 40)
    print(f"✅ Duplicates identified: {stats['duplicates']}")
    print(f"⚡ Clashes identified: {stats['clashes']}")
    print(f"📦 Original total size: {stats['total_size']:,} bytes")
    print(f"🚀 Consolidated critical: {consolidated_stats['critical_size']:,} bytes")
    print(f"🚀 Consolidated non-critical: {consolidated_stats['non_critical_size']:,} bytes")
    print(f"📦 Gzipped critical: {consolidated_stats['critical_gzipped']:,} bytes")
    print(f"📦 Gzipped non-critical: {consolidated_stats['non_critical_gzipped']:,} bytes")
    
    total_optimized = consolidated_stats['critical_gzipped'] + consolidated_stats['non_critical_gzipped']
    reduction = ((stats['total_size'] - total_optimized) / stats['total_size']) * 100
    print(f"💾 Total reduction: {reduction:.1f}%")

if __name__ == "__main__":
    main()
